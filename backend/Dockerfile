FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY backend/package*.json ./

# Install dependencies
RUN npm ci

# Copy shared types
COPY shared ../shared

# Copy backend source code
COPY backend .

# Build the application
RUN npm run build

# Copy shared types to dist folder for runtime
RUN cp -r ../shared ./dist/

# Expose port (Cloud Run uses PORT env var)
EXPOSE $PORT

# Start the application
CMD ["npm", "start"]
