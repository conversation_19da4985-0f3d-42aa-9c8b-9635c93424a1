# Database Configuration
# Local PostgreSQL for development
# DATABASE_URL=postgresql://postgres:@localhost:5432/ora_hume_db
# Cloud database (GCP Cloud SQL) - uncomment when GCP connection is ready
DATABASE_URL=postgresql://ora_user:@**************:5432/ora_hume_db

# JWT Configuration
JWT_SECRET=ud+yoZe4Ymym+9LZbU2zheI9kT0Jww8ONCsC1/oRQ+s=
JWT_EXPIRES_IN=7d

# Google OAuth Configuration
GOOGLE_CLIENT_ID=222129249954-rk4mmk3gf8egug0e0ubm479to12r6ba1.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Hume AI Configuration
HUME_API_KEY=ofPMZlMGpzwv1FkzY5LRVpFksxTlobYrEiAqG0AK9QYPfrmK
HUME_SECRET_KEY=gMuDaoQ57BAJzCzUPagUdMPCG0FGKIDY49rqCGI8mwO8GKGVPewU7zuItp9oCkAh
HUME_CONFIG_ID=4c094e4c-e9a3-42f9-91de-727831eaa0f5
HUME_BASE_URL=https://api.hume.ai

# Server Configuration
PORT=3001
NODE_ENV=development
BACKEND_URL=http://localhost:3001

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# Session Configuration
SESSION_SECRET=roNIPT7hAktqrn3hmoyVLEyJwyib+VwCB6xKNbGQ1T8=

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_here

# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=ora-phase1
GOOGLE_CLOUD_STORAGE_BUCKET=ora-voice-storage
# For development, we'll use application default credentials
# GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# Admin Configuration
DEFAULT_ADMIN_EMAIL=<EMAIL>
