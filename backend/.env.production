# Production Environment Variables for GCP Cloud Run
# These will be set in Cloud Run console

# Server Configuration
NODE_ENV=production
PORT=8080

# Database Configuration (Cloud SQL)
# Format: *******************************************************************
DATABASE_URL=postgresql://ora_user:@************:5432/ora_hume_db

# Google OAuth Configuration
GOOGLE_CLIENT_ID=YOUR_GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=YOUR_GOOGLE_CLIENT_SECRET

# JWT Configuration
JWT_SECRET=YOUR_PRODUCTION_JWT_SECRET
JWT_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=YOUR_PRODUCTION_SESSION_SECRET

# Hume AI Configuration
HUME_API_KEY=YOUR_HUME_API_KEY
HUME_SECRET_KEY=YOUR_HUME_SECRET_KEY
HUME_CONFIG_ID=YOUR_HUME_CONFIG_ID
HUME_BASE_URL=https://api.hume.ai

# Backend URL (for OAuth redirects)
BACKEND_URL=https://ora-backend-************.us-central1.run.app

# Frontend URL (will be updated after deployment)
FRONTEND_URL=https://ora-frontend-************.us-central1.run.app

# CORS Origins
CORS_ORIGINS=https://ora-frontend-************.us-central1.run.app

# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=ora-phase1
GOOGLE_CLOUD_STORAGE_BUCKET=ora-voice-storage
# In production, use service account key or workload identity
# GOOGLE_APPLICATION_CREDENTIALS=/app/service-account-key.json

# Admin Configuration
DEFAULT_ADMIN_EMAIL=<EMAIL>
