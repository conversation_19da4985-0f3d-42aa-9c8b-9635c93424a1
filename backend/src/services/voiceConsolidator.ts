import { VoiceStorageQueueModel } from '../database/models/VoiceStorageQueue.js';
import { AudioDataModel } from '../database/models/AudioData.js';
import { voiceStorageService } from './voiceStorage.js';
import type { VoiceStorageQueueItem } from '../database/models/VoiceStorageQueue.js';

export interface ConsolidatedAudioData {
  sessionId: string;
  userId: string;
  role: 'user' | 'assistant';
  chunks: VoiceStorageQueueItem[];
  totalSize: number;
  duration: number;
  startTime: Date;
  endTime: Date;
}

export class VoiceConsolidatorService {
  private consolidationInterval: NodeJS.Timeout | null = null;
  private isConsolidating = false;
  private readonly CONSOLIDATION_INTERVAL_MS = 10000; // Check every 10 seconds
  private readonly CHUNK_TIMEOUT_MS = 5000; // Wait 5 seconds after last chunk before consolidating
  private readonly MAX_CHUNKS_PER_FILE = 100; // Maximum chunks to consolidate into one file

  constructor() {
    console.log('🎵 VoiceConsolidatorService initialized');
  }

  /**
   * Initialize the voice consolidator and start background consolidation
   */
  async initialize(): Promise<void> {
    try {
      // Start background consolidation
      this.startBackgroundConsolidation();
      
      console.log('✅ VoiceConsolidatorService initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing VoiceConsolidatorService:', error);
      throw error;
    }
  }

  /**
   * Start background consolidation of audio chunks
   */
  private startBackgroundConsolidation(): void {
    if (this.consolidationInterval) {
      return; // Already started
    }

    console.log('🔄 Starting background voice consolidation...');
    
    this.consolidationInterval = setInterval(async () => {
      if (this.isConsolidating) {
        return; // Skip if already consolidating
      }

      try {
        await this.consolidateReadyChunks();
      } catch (error) {
        console.error('❌ Error in background voice consolidation:', error);
      }
    }, this.CONSOLIDATION_INTERVAL_MS);
  }

  /**
   * Stop background consolidation
   */
  stopBackgroundConsolidation(): void {
    if (this.consolidationInterval) {
      clearInterval(this.consolidationInterval);
      this.consolidationInterval = null;
      console.log('⏹️ Background voice consolidation stopped');
    }
  }

  /**
   * Find and consolidate audio chunks that are ready
   */
  private async consolidateReadyChunks(): Promise<void> {
    if (this.isConsolidating) {
      return;
    }

    this.isConsolidating = true;

    try {
      // Find chunks that haven't been updated recently (indicating end of stream)
      const cutoffTime = new Date(Date.now() - this.CHUNK_TIMEOUT_MS);
      const readyChunks = await VoiceStorageQueueModel.findChunksReadyForConsolidation(cutoffTime);
      
      if (readyChunks.length === 0) {
        return; // No chunks ready for consolidation
      }

      console.log(`🔄 Found ${readyChunks.length} chunks ready for consolidation`);

      // Group chunks by session, user, and role
      const groupedChunks = this.groupChunksByContext(readyChunks);

      // Consolidate each group
      for (const group of groupedChunks) {
        await this.consolidateChunkGroup(group);
      }

    } catch (error) {
      console.error('❌ Error consolidating ready chunks:', error);
    } finally {
      this.isConsolidating = false;
    }
  }

  /**
   * Group chunks by session, user, and role for consolidation
   */
  private groupChunksByContext(chunks: VoiceStorageQueueItem[]): ConsolidatedAudioData[] {
    const groups = new Map<string, VoiceStorageQueueItem[]>();

    for (const chunk of chunks) {
      const key = `${chunk.sessionId}-${chunk.userId}-${chunk.role}`;
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(chunk);
    }

    return Array.from(groups.entries()).map(([key, chunkGroup]) => {
      const [sessionId, userId, role] = key.split('-');
      const sortedChunks = chunkGroup.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
      
      return {
        sessionId,
        userId,
        role: role as 'user' | 'assistant',
        chunks: sortedChunks,
        totalSize: sortedChunks.reduce((sum, chunk) => sum + chunk.audioData.length, 0),
        duration: this.calculateDuration(sortedChunks),
        startTime: sortedChunks[0].createdAt,
        endTime: sortedChunks[sortedChunks.length - 1].createdAt
      };
    });
  }

  /**
   * Calculate estimated duration from chunks
   */
  private calculateDuration(chunks: VoiceStorageQueueItem[]): number {
    // Estimate duration based on time span and audio data size
    const timeSpan = chunks[chunks.length - 1].createdAt.getTime() - chunks[0].createdAt.getTime();
    return Math.max(timeSpan, chunks.length * 100); // Minimum 100ms per chunk
  }

  /**
   * Consolidate a group of chunks into a single audio file
   */
  private async consolidateChunkGroup(group: ConsolidatedAudioData): Promise<void> {
    try {
      console.log(`🎵 Consolidating ${group.chunks.length} chunks for ${group.role} in session ${group.sessionId}`);

      // Mark chunks as processing
      await Promise.all(
        group.chunks.map(chunk => VoiceStorageQueueModel.markAsProcessing(chunk.id))
      );

      // Consolidate audio data (simple concatenation for now)
      const consolidatedAudioData = this.consolidateAudioData(group.chunks);

      // Create consolidated filename
      const timestamp = group.startTime.toISOString().split('T')[0];
      const timeRange = `${group.startTime.toISOString().split('T')[1].split('.')[0]}-${group.endTime.toISOString().split('T')[1].split('.')[0]}`;
      const fileName = `${group.role}_${timestamp}_${timeRange}_consolidated.webm`;

      // Upload consolidated file
      const voiceMetadata = await voiceStorageService.uploadVoiceData({
        userId: group.userId,
        sessionId: group.sessionId,
        messageId: undefined, // No specific message for consolidated audio
        role: group.role,
        audioData: consolidatedAudioData,
        mimeType: group.chunks[0].mimeType,
        timestamp: group.startTime,
        metadata: {
          consolidatedChunks: group.chunks.length,
          originalChunkIds: group.chunks.map(c => c.id),
          duration: group.duration,
          consolidatedAt: new Date().toISOString(),
          source: 'voice_consolidator'
        },
        customFileName: fileName
      });

      // Save consolidated audio data record
      await AudioDataModel.createFromVoiceMetadata(voiceMetadata);

      // Mark all chunks as completed
      await Promise.all(
        group.chunks.map(chunk => VoiceStorageQueueModel.markAsCompleted(chunk.id))
      );

      console.log(`✅ Successfully consolidated ${group.chunks.length} chunks into ${fileName}`);

    } catch (error) {
      console.error(`❌ Error consolidating chunk group:`, error);
      
      // Mark chunks as failed
      await Promise.all(
        group.chunks.map(chunk => 
          VoiceStorageQueueModel.markAsFailed(
            chunk.id, 
            error instanceof Error ? error.message : 'Consolidation failed'
          )
        )
      );
    }
  }

  /**
   * Consolidate audio data from multiple chunks
   * For now, this is simple concatenation. In the future, we could:
   * - Properly merge audio streams
   * - Apply compression
   * - Remove silence gaps
   */
  private consolidateAudioData(chunks: VoiceStorageQueueItem[]): string {
    // Sort chunks by creation time to maintain order
    const sortedChunks = chunks.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
    
    // For base64 audio data, we need to decode, concatenate, and re-encode
    // For now, we'll do simple concatenation (this works for some audio formats)
    return sortedChunks.map(chunk => chunk.audioData).join('');
  }

  /**
   * Manually trigger consolidation for a specific session
   */
  async consolidateSession(sessionId: string): Promise<number> {
    try {
      console.log(`🔄 Manually consolidating session: ${sessionId}`);
      
      const sessionChunks = await VoiceStorageQueueModel.findBySessionId(sessionId, 'pending');
      if (sessionChunks.length === 0) {
        console.log(`ℹ️ No pending chunks found for session ${sessionId}`);
        return 0;
      }

      const groupedChunks = this.groupChunksByContext(sessionChunks);
      
      for (const group of groupedChunks) {
        await this.consolidateChunkGroup(group);
      }

      console.log(`✅ Consolidated ${sessionChunks.length} chunks for session ${sessionId}`);
      return sessionChunks.length;

    } catch (error) {
      console.error(`❌ Error consolidating session ${sessionId}:`, error);
      return 0;
    }
  }

  /**
   * Get consolidation statistics
   */
  async getConsolidationStats(): Promise<{
    pendingChunks: number;
    readyForConsolidation: number;
    consolidatedFiles: number;
    isConsolidating: boolean;
  }> {
    try {
      const [pendingChunks, consolidatedFiles] = await Promise.all([
        VoiceStorageQueueModel.getQueueStats(),
        AudioDataModel.getStorageStats()
      ]);

      const cutoffTime = new Date(Date.now() - this.CHUNK_TIMEOUT_MS);
      const readyChunks = await VoiceStorageQueueModel.findChunksReadyForConsolidation(cutoffTime);

      return {
        pendingChunks: pendingChunks.pending || 0,
        readyForConsolidation: readyChunks.length,
        consolidatedFiles: consolidatedFiles.totalFiles || 0,
        isConsolidating: this.isConsolidating
      };
    } catch (error) {
      console.error('❌ Error getting consolidation stats:', error);
      return {
        pendingChunks: 0,
        readyForConsolidation: 0,
        consolidatedFiles: 0,
        isConsolidating: this.isConsolidating
      };
    }
  }

  /**
   * Get health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    isConsolidating: boolean;
    backgroundConsolidationActive: boolean;
  } {
    return {
      status: this.consolidationInterval ? 'healthy' : 'degraded',
      isConsolidating: this.isConsolidating,
      backgroundConsolidationActive: this.consolidationInterval !== null
    };
  }
}

// Create singleton instance
export const voiceConsolidatorService = new VoiceConsolidatorService();
