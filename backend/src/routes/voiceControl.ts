import express from 'express';
import { voiceProcessorService } from '../services/voiceProcessor.js';
import { voiceConsolidatorService } from '../services/voiceConsolidator.js';
import { VoiceStorageQueueModel } from '../database/models/VoiceStorageQueue.js';
import { authMiddleware } from '../middleware/auth.js';

const router = express.Router();

// Apply authentication to all routes
router.use(authMiddleware.authenticate);

/**
 * Get voice storage system status
 */
router.get('/status', async (req, res) => {
  try {
    const [processorHealth, consolidatorHealth, processingStats, consolidationStats] = await Promise.all([
      voiceProcessorService.getHealthStatus(),
      voiceConsolidatorService.getHealthStatus(),
      voiceProcessorService.getProcessingStats(),
      voiceConsolidatorService.getConsolidationStats()
    ]);

    res.json({
      success: true,
      data: {
        processor: processorHealth,
        consolidator: consolidatorHealth,
        processing: processingStats,
        consolidation: consolidationStats,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('❌ Error getting voice storage status:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STATUS_ERROR',
        message: 'Failed to get voice storage status'
      }
    });
  }
});

/**
 * Control consolidation mode
 */
router.post('/consolidation/toggle', async (req, res) => {
  try {
    const { enabled } = req.body;
    
    if (typeof enabled !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'enabled must be a boolean'
        }
      });
    }

    voiceProcessorService.setConsolidationMode(enabled);

    res.json({
      success: true,
      data: {
        consolidationMode: enabled,
        message: `Consolidation mode ${enabled ? 'enabled' : 'disabled'}`
      }
    });
  } catch (error) {
    console.error('❌ Error toggling consolidation mode:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'TOGGLE_ERROR',
        message: 'Failed to toggle consolidation mode'
      }
    });
  }
});

/**
 * Manually trigger consolidation for a session
 */
router.post('/consolidation/session/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    const consolidatedCount = await voiceProcessorService.consolidateSession(sessionId);

    res.json({
      success: true,
      data: {
        sessionId,
        consolidatedChunks: consolidatedCount,
        message: `Consolidated ${consolidatedCount} chunks for session ${sessionId}`
      }
    });
  } catch (error) {
    console.error('❌ Error consolidating session:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CONSOLIDATION_ERROR',
        message: 'Failed to consolidate session'
      }
    });
  }
});

/**
 * Clean up old completed queue items
 */
router.post('/cleanup', async (req, res) => {
  try {
    const { olderThanDays = 7 } = req.body;
    
    const deletedCount = await voiceProcessorService.cleanupQueue(olderThanDays);

    res.json({
      success: true,
      data: {
        deletedCount,
        olderThanDays,
        message: `Cleaned up ${deletedCount} completed items older than ${olderThanDays} days`
      }
    });
  } catch (error) {
    console.error('❌ Error cleaning up queue:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CLEANUP_ERROR',
        message: 'Failed to clean up queue'
      }
    });
  }
});

/**
 * Get queue statistics
 */
router.get('/queue/stats', async (req, res) => {
  try {
    const stats = await VoiceStorageQueueModel.getQueueStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('❌ Error getting queue stats:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STATS_ERROR',
        message: 'Failed to get queue statistics'
      }
    });
  }
});

/**
 * Manually trigger processing
 */
router.post('/process/trigger', async (req, res) => {
  try {
    await voiceProcessorService.triggerProcessing();

    res.json({
      success: true,
      data: {
        message: 'Processing triggered successfully'
      }
    });
  } catch (error) {
    console.error('❌ Error triggering processing:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'TRIGGER_ERROR',
        message: 'Failed to trigger processing'
      }
    });
  }
});

/**
 * Emergency stop all processing
 */
router.post('/emergency/stop', async (req, res) => {
  try {
    voiceProcessorService.stopBackgroundProcessing();
    voiceConsolidatorService.stopBackgroundConsolidation();

    res.json({
      success: true,
      data: {
        message: 'All voice processing stopped'
      }
    });
  } catch (error) {
    console.error('❌ Error stopping processing:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STOP_ERROR',
        message: 'Failed to stop processing'
      }
    });
  }
});

/**
 * Emergency restart all processing
 */
router.post('/emergency/restart', async (req, res) => {
  try {
    // Stop first
    voiceProcessorService.stopBackgroundProcessing();
    voiceConsolidatorService.stopBackgroundConsolidation();

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Restart
    await voiceProcessorService.initialize();

    res.json({
      success: true,
      data: {
        message: 'Voice processing restarted successfully'
      }
    });
  } catch (error) {
    console.error('❌ Error restarting processing:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'RESTART_ERROR',
        message: 'Failed to restart processing'
      }
    });
  }
});

export default router;
