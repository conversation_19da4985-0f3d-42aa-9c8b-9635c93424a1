import { query } from '../config.js';

export interface VoiceStorageQueueItem {
  id: string;
  messageId?: string; // Made optional for raw audio chunks
  userId: string;
  sessionId: string;
  role: 'user' | 'assistant';
  audioData: string; // base64 encoded
  mimeType: string;
  priority: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';
  retryCount: number;
  maxRetries: number;
  errorMessage?: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;
}

export interface DatabaseVoiceStorageQueueItem {
  id: string;
  message_id?: string; // Made optional for raw audio chunks
  user_id: string;
  session_id: string;
  role: string;
  audio_data: string;
  mime_type: string;
  priority: number;
  status: string;
  retry_count: number;
  max_retries: number;
  error_message?: string;
  metadata?: any;
  created_at: Date;
  updated_at: Date;
  processed_at?: Date;
}

export class VoiceStorageQueueModel {
  static async findById(id: string): Promise<VoiceStorageQueueItem | null> {
    const result = await query(
      'SELECT * FROM voice_storage_queue WHERE id = $1',
      [id]
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseQueueItemToQueueItem(result.rows[0]);
  }

  static async findPendingItems(limit: number = 10): Promise<VoiceStorageQueueItem[]> {
    const result = await query(
      `SELECT * FROM voice_storage_queue 
       WHERE status IN ('pending', 'retrying') 
       ORDER BY priority ASC, created_at ASC 
       LIMIT $1`,
      [limit]
    );
    
    return result.rows.map(this.mapDatabaseQueueItemToQueueItem);
  }

  static async findByStatus(status: string, limit: number = 100, offset: number = 0): Promise<VoiceStorageQueueItem[]> {
    const result = await query(
      'SELECT * FROM voice_storage_queue WHERE status = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3',
      [status, limit, offset]
    );
    
    return result.rows.map(this.mapDatabaseQueueItemToQueueItem);
  }

  static async findByUserId(userId: string, limit: number = 100, offset: number = 0): Promise<VoiceStorageQueueItem[]> {
    const result = await query(
      'SELECT * FROM voice_storage_queue WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3',
      [userId, limit, offset]
    );
    
    return result.rows.map(this.mapDatabaseQueueItemToQueueItem);
  }

  static async create(queueData: {
    messageId?: string; // Made optional for raw audio chunks
    userId: string;
    sessionId: string;
    role: 'user' | 'assistant';
    audioData: string;
    mimeType: string;
    priority?: number;
    maxRetries?: number;
    metadata?: any;
  }): Promise<VoiceStorageQueueItem> {
    const result = await query(
      `INSERT INTO voice_storage_queue (
        message_id, user_id, session_id, role, audio_data, mime_type, 
        priority, max_retries, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
      RETURNING *`,
      [
        queueData.messageId,
        queueData.userId,
        queueData.sessionId,
        queueData.role,
        queueData.audioData,
        queueData.mimeType,
        queueData.priority || 5,
        queueData.maxRetries || 3,
        JSON.stringify(queueData.metadata || {})
      ]
    );
    
    return this.mapDatabaseQueueItemToQueueItem(result.rows[0]);
  }

  static async updateStatus(id: string, updates: {
    status?: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';
    retryCount?: number;
    errorMessage?: string;
    processedAt?: Date;
  }): Promise<VoiceStorageQueueItem | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (updates.status !== undefined) {
      setParts.push(`status = $${paramCount++}`);
      values.push(updates.status);
    }

    if (updates.retryCount !== undefined) {
      setParts.push(`retry_count = $${paramCount++}`);
      values.push(updates.retryCount);
    }

    if (updates.errorMessage !== undefined) {
      setParts.push(`error_message = $${paramCount++}`);
      values.push(updates.errorMessage);
    }

    if (updates.processedAt !== undefined) {
      setParts.push(`processed_at = $${paramCount++}`);
      values.push(updates.processedAt);
    }

    if (setParts.length === 0) {
      throw new Error('No updates provided');
    }

    values.push(id);
    
    const result = await query(
      `UPDATE voice_storage_queue SET ${setParts.join(', ')}, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $${paramCount} 
       RETURNING *`,
      values
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseQueueItemToQueueItem(result.rows[0]);
  }

  static async markAsProcessing(id: string): Promise<VoiceStorageQueueItem | null> {
    return this.updateStatus(id, { status: 'processing' });
  }

  static async markAsCompleted(id: string): Promise<VoiceStorageQueueItem | null> {
    return this.updateStatus(id, { 
      status: 'completed', 
      processedAt: new Date() 
    });
  }

  static async markAsFailed(id: string, errorMessage: string): Promise<VoiceStorageQueueItem | null> {
    const item = await this.findById(id);
    if (!item) return null;

    const newRetryCount = item.retryCount + 1;
    const shouldRetry = newRetryCount < item.maxRetries;

    return this.updateStatus(id, {
      status: shouldRetry ? 'retrying' : 'failed',
      retryCount: newRetryCount,
      errorMessage,
      processedAt: shouldRetry ? undefined : new Date()
    });
  }

  static async delete(id: string): Promise<boolean> {
    const result = await query(
      'DELETE FROM voice_storage_queue WHERE id = $1',
      [id]
    );
    
    return result.rowCount > 0;
  }

  static async cleanupCompleted(olderThanDays: number = 7): Promise<number> {
    const result = await query(
      `DELETE FROM voice_storage_queue 
       WHERE status = 'completed' 
       AND processed_at < CURRENT_TIMESTAMP - INTERVAL '${olderThanDays} days'`,
      []
    );
    
    return result.rowCount;
  }

  static async getQueueStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    retrying: number;
    total: number;
  }> {
    const result = await query(`
      SELECT 
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN status = 'retrying' THEN 1 END) as retrying,
        COUNT(*) as total
      FROM voice_storage_queue
    `, []);

    const row = result.rows[0];
    return {
      pending: parseInt(row.pending) || 0,
      processing: parseInt(row.processing) || 0,
      completed: parseInt(row.completed) || 0,
      failed: parseInt(row.failed) || 0,
      retrying: parseInt(row.retrying) || 0,
      total: parseInt(row.total) || 0
    };
  }

  static async getOldestPendingItem(): Promise<VoiceStorageQueueItem | null> {
    const result = await query(
      `SELECT * FROM voice_storage_queue
       WHERE status IN ('pending', 'retrying')
       ORDER BY priority ASC, created_at ASC
       LIMIT 1`,
      []
    );

    if (result.rows.length === 0) return null;

    return this.mapDatabaseQueueItemToQueueItem(result.rows[0]);
  }

  /**
   * Find chunks ready for consolidation (not updated recently)
   */
  static async findChunksReadyForConsolidation(cutoffTime: Date): Promise<VoiceStorageQueueItem[]> {
    const result = await query(
      `SELECT * FROM voice_storage_queue
       WHERE status = 'pending'
       AND updated_at < $1
       ORDER BY session_id, user_id, role, created_at ASC`,
      [cutoffTime]
    );

    return result.rows.map(this.mapDatabaseQueueItemToQueueItem);
  }

  /**
   * Find queue items by session ID and status
   */
  static async findBySessionId(sessionId: string, status?: string): Promise<VoiceStorageQueueItem[]> {
    const whereClause = status ? 'WHERE session_id = $1 AND status = $2' : 'WHERE session_id = $1';
    const params = status ? [sessionId, status] : [sessionId];

    const result = await query(
      `SELECT * FROM voice_storage_queue
       ${whereClause}
       ORDER BY created_at ASC`,
      params
    );

    return result.rows.map(this.mapDatabaseQueueItemToQueueItem);
  }

  private static mapDatabaseQueueItemToQueueItem(dbItem: DatabaseVoiceStorageQueueItem): VoiceStorageQueueItem {
    return {
      id: dbItem.id,
      messageId: dbItem.message_id || undefined, // Handle optional message_id
      userId: dbItem.user_id,
      sessionId: dbItem.session_id,
      role: dbItem.role as 'user' | 'assistant',
      audioData: dbItem.audio_data,
      mimeType: dbItem.mime_type,
      priority: dbItem.priority,
      status: dbItem.status as 'pending' | 'processing' | 'completed' | 'failed' | 'retrying',
      retryCount: dbItem.retry_count,
      maxRetries: dbItem.max_retries,
      errorMessage: dbItem.error_message,
      metadata: dbItem.metadata || {},
      createdAt: new Date(dbItem.created_at),
      updatedAt: new Date(dbItem.updated_at),
      processedAt: dbItem.processed_at ? new Date(dbItem.processed_at) : undefined
    };
  }
}
