import { query } from '../config.js';
import type { VoiceFileMetadata } from '../../services/voiceStorage.js';

export interface AudioData {
  id: string;
  messageId?: string; // Made optional for raw audio chunks
  userId: string;
  sessionId: string;
  role: 'user' | 'assistant';
  fileName: string;
  filePath: string;
  storageUrl: string;
  signedUrl?: string;
  signedUrlExpiresAt?: Date;
  duration?: number;
  fileSize: number;
  mimeType: string;
  storageProvider: string;
  uploadStatus: 'pending' | 'uploading' | 'completed' | 'failed';
  uploadError?: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface DatabaseAudioData {
  id: string;
  message_id?: string; // Made optional for raw audio chunks
  user_id: string;
  session_id: string;
  role: string;
  file_name: string;
  file_path: string;
  storage_url: string;
  signed_url?: string;
  signed_url_expires_at?: Date;
  duration?: number;
  file_size: number;
  mime_type: string;
  storage_provider: string;
  upload_status: string;
  upload_error?: string;
  metadata?: any;
  created_at: Date;
  updated_at: Date;
}

export class AudioDataModel {
  static async findById(id: string): Promise<AudioData | null> {
    const result = await query(
      'SELECT * FROM audio_data WHERE id = $1',
      [id]
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseAudioDataToAudioData(result.rows[0]);
  }

  static async findByMessageId(messageId: string): Promise<AudioData[]> {
    const result = await query(
      'SELECT * FROM audio_data WHERE message_id = $1 ORDER BY created_at ASC',
      [messageId]
    );
    
    return result.rows.map(this.mapDatabaseAudioDataToAudioData);
  }

  static async findBySessionId(sessionId: string, limit: number = 100, offset: number = 0): Promise<AudioData[]> {
    const result = await query(
      'SELECT * FROM audio_data WHERE session_id = $1 ORDER BY created_at ASC LIMIT $2 OFFSET $3',
      [sessionId, limit, offset]
    );
    
    return result.rows.map(this.mapDatabaseAudioDataToAudioData);
  }

  static async findByUserId(userId: string, limit: number = 100, offset: number = 0): Promise<AudioData[]> {
    const result = await query(
      'SELECT * FROM audio_data WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3',
      [userId, limit, offset]
    );
    
    return result.rows.map(this.mapDatabaseAudioDataToAudioData);
  }

  static async create(audioData: {
    messageId?: string; // Made optional for raw audio chunks
    userId: string;
    sessionId: string;
    role: 'user' | 'assistant';
    fileName: string;
    filePath: string;
    storageUrl: string;
    fileSize: number;
    mimeType: string;
    duration?: number;
    storageProvider?: string;
    uploadStatus?: 'pending' | 'uploading' | 'completed' | 'failed';
    metadata?: any;
  }): Promise<AudioData> {
    const result = await query(
      `INSERT INTO audio_data (
        message_id, user_id, session_id, role, file_name, file_path, 
        storage_url, file_size, mime_type, duration, storage_provider, 
        upload_status, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) 
      RETURNING *`,
      [
        audioData.messageId,
        audioData.userId,
        audioData.sessionId,
        audioData.role,
        audioData.fileName,
        audioData.filePath,
        audioData.storageUrl,
        audioData.fileSize,
        audioData.mimeType,
        audioData.duration,
        audioData.storageProvider || 'gcs',
        audioData.uploadStatus || 'pending',
        JSON.stringify(audioData.metadata || {})
      ]
    );
    
    return this.mapDatabaseAudioDataToAudioData(result.rows[0]);
  }

  static async createFromVoiceMetadata(voiceMetadata: VoiceFileMetadata): Promise<AudioData> {
    return this.create({
      messageId: voiceMetadata.messageId,
      userId: voiceMetadata.userId,
      sessionId: voiceMetadata.sessionId,
      role: voiceMetadata.role,
      fileName: voiceMetadata.fileName,
      filePath: voiceMetadata.filePath,
      storageUrl: voiceMetadata.publicUrl,
      fileSize: voiceMetadata.fileSize,
      mimeType: voiceMetadata.mimeType,
      duration: voiceMetadata.duration,
      uploadStatus: 'completed',
      metadata: voiceMetadata.metadata
    });
  }

  static async update(id: string, updates: {
    signedUrl?: string;
    signedUrlExpiresAt?: Date;
    duration?: number;
    uploadStatus?: 'pending' | 'uploading' | 'completed' | 'failed';
    uploadError?: string;
    metadata?: any;
  }): Promise<AudioData | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (updates.signedUrl !== undefined) {
      setParts.push(`signed_url = $${paramCount++}`);
      values.push(updates.signedUrl);
    }

    if (updates.signedUrlExpiresAt !== undefined) {
      setParts.push(`signed_url_expires_at = $${paramCount++}`);
      values.push(updates.signedUrlExpiresAt);
    }

    if (updates.duration !== undefined) {
      setParts.push(`duration = $${paramCount++}`);
      values.push(updates.duration);
    }

    if (updates.uploadStatus !== undefined) {
      setParts.push(`upload_status = $${paramCount++}`);
      values.push(updates.uploadStatus);
    }

    if (updates.uploadError !== undefined) {
      setParts.push(`upload_error = $${paramCount++}`);
      values.push(updates.uploadError);
    }

    if (updates.metadata !== undefined) {
      setParts.push(`metadata = $${paramCount++}`);
      values.push(JSON.stringify(updates.metadata));
    }

    if (setParts.length === 0) {
      throw new Error('No updates provided');
    }

    values.push(id);
    
    const result = await query(
      `UPDATE audio_data SET ${setParts.join(', ')}, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $${paramCount} 
       RETURNING *`,
      values
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseAudioDataToAudioData(result.rows[0]);
  }

  static async delete(id: string): Promise<boolean> {
    const result = await query(
      'DELETE FROM audio_data WHERE id = $1',
      [id]
    );
    
    return result.rowCount > 0;
  }

  static async getStorageStats(userId?: string): Promise<{
    totalFiles: number;
    totalSize: number;
    userFiles?: number;
    assistantFiles?: number;
  }> {
    let whereClause = '';
    const params: any[] = [];
    
    if (userId) {
      whereClause = 'WHERE user_id = $1';
      params.push(userId);
    }

    const result = await query(`
      SELECT 
        COUNT(*) as total_files,
        SUM(file_size) as total_size,
        COUNT(CASE WHEN role = 'user' THEN 1 END) as user_files,
        COUNT(CASE WHEN role = 'assistant' THEN 1 END) as assistant_files
      FROM audio_data 
      ${whereClause}
    `, params);

    const row = result.rows[0];
    return {
      totalFiles: parseInt(row.total_files) || 0,
      totalSize: parseInt(row.total_size) || 0,
      userFiles: parseInt(row.user_files) || 0,
      assistantFiles: parseInt(row.assistant_files) || 0
    };
  }

  static async cleanupExpiredSignedUrls(): Promise<number> {
    const result = await query(
      `UPDATE audio_data 
       SET signed_url = NULL, signed_url_expires_at = NULL, updated_at = CURRENT_TIMESTAMP
       WHERE signed_url_expires_at < CURRENT_TIMESTAMP`,
      []
    );
    
    return result.rowCount;
  }

  private static mapDatabaseAudioDataToAudioData(dbAudioData: DatabaseAudioData): AudioData {
    return {
      id: dbAudioData.id,
      messageId: dbAudioData.message_id || undefined, // Handle optional message_id
      userId: dbAudioData.user_id,
      sessionId: dbAudioData.session_id,
      role: dbAudioData.role as 'user' | 'assistant',
      fileName: dbAudioData.file_name,
      filePath: dbAudioData.file_path,
      storageUrl: dbAudioData.storage_url,
      signedUrl: dbAudioData.signed_url,
      signedUrlExpiresAt: dbAudioData.signed_url_expires_at ? new Date(dbAudioData.signed_url_expires_at) : undefined,
      duration: dbAudioData.duration,
      fileSize: dbAudioData.file_size,
      mimeType: dbAudioData.mime_type,
      storageProvider: dbAudioData.storage_provider,
      uploadStatus: dbAudioData.upload_status as 'pending' | 'uploading' | 'completed' | 'failed',
      uploadError: dbAudioData.upload_error,
      metadata: dbAudioData.metadata || {},
      createdAt: new Date(dbAudioData.created_at),
      updatedAt: new Date(dbAudioData.updated_at)
    };
  }
}
