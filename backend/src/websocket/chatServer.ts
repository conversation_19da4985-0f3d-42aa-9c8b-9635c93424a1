import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { URL } from 'url';
import { HumeEVIService } from '../hume/eviService.js';
import { authMiddleware } from '../middleware/auth.js';
import { voiceProcessorService } from '../services/voiceProcessor.js';
import type { User, ChatSession, WebSocketMessage } from '../../../shared/types.js';
import type { Hume } from 'hume';

interface AuthenticatedWebSocket extends WebSocket {
  user?: User;
  session?: ChatSession;
  humeSocket?: Hume.empathicVoice.chat.ChatSocket;
  isAlive?: boolean;
}

export class ChatWebSocketServer {
  private wss: WebSocketServer;
  private humeService: HumeEVIService;
  private clients: Set<AuthenticatedWebSocket> = new Set();

  constructor(server: any, path: string = '/ws/chat') {
    this.humeService = new HumeEVIService();
    
    this.wss = new WebSocketServer({
      server,
      path,
      verifyClient: this.verifyClient.bind(this)
    });

    this.setupServer();
    this.startHeartbeat();
  }

  /**
   * Verify client authentication before WebSocket upgrade
   */
  private verifyClient(
    info: { origin: string; secure: boolean; req: IncomingMessage },
    callback: (res: boolean, code?: number, message?: string) => void
  ): void {
    (async () => {
      try {
        console.log('🔍 WebSocket verification attempt:', {
          url: info.req.url,
          origin: info.origin,
          secure: info.secure
        });

        const url = new URL(info.req.url!, `http://${info.req.headers.host}`);
        const token = url.searchParams.get('token');

        console.log('🔑 Extracted token:', token ? 'Token present' : 'No token');

        if (!token) {
          console.log('❌ WebSocket connection rejected: No token provided');
          callback(false, 401, 'No token provided');
          return;
        }

        const user = await authMiddleware.validateSession(token);
        console.log('👤 User validation result:', user ? `User found: ${user.email}` : 'No user found');

        if (!user) {
          console.log('❌ WebSocket connection rejected: Invalid token');
          callback(false, 401, 'Invalid token');
          return;
        }

        // Store user in request for later use
        (info.req as any).user = user;
        console.log('✅ WebSocket verification successful for user:', user.email);
        callback(true);

      } catch (error) {
        console.error('WebSocket verification error:', error);
        callback(false, 500, 'Internal server error');
      }
    })();
  }

  /**
   * Set up WebSocket server event handlers
   */
  private setupServer(): void {
    this.wss.on('connection', async (ws: AuthenticatedWebSocket, req: IncomingMessage) => {
      try {
        // Get user from verification step
        console.log('🔗 WebSocket connection attempt, req.user:', (req as any).user);
        ws.user = (req as any).user;
        ws.isAlive = true;

        if (!ws.user) {
          console.error('❌ WebSocket connection setup error: No user found in request');
          ws.close(1008, 'Authentication failed');
          return;
        }

        console.log(`🔗 WebSocket connected for user: ${ws.user.email}`);
        this.clients.add(ws);

        // Set up client event handlers
        this.setupClientHandlers(ws);

        // Send welcome message
        this.sendMessage(ws, {
          type: 'connection_established',
          data: {
            userId: ws.user!.id,
            message: 'Connected to ORA Hume chat server'
          },
          timestamp: new Date()
        });

      } catch (error) {
        console.error('WebSocket connection setup error:', error);
        ws.close(1011, 'Server error during connection setup');
      }
    });

    this.wss.on('error', (error) => {
      console.error('WebSocket server error:', error);
    });

    console.log('🚀 Chat WebSocket server started');
  }

  /**
   * Set up individual client event handlers
   */
  private setupClientHandlers(ws: AuthenticatedWebSocket): void {
    ws.on('message', async (data: Buffer) => {
      try {
        const message = JSON.parse(data.toString()) as WebSocketMessage;
        await this.handleClientMessage(ws, message);
      } catch (error) {
        console.error('Error handling client message:', error);
        this.sendError(ws, 'INVALID_MESSAGE', 'Failed to process message');
      }
    });

    ws.on('close', async (code: number, reason: Buffer) => {
      console.log(`🔌 WebSocket disconnected for user: ${ws.user?.email}, code: ${code}, reason: ${reason.toString()}`);
      await this.handleClientDisconnect(ws);
      this.clients.delete(ws);
    });

    ws.on('error', (error: Error) => {
      console.error(`WebSocket error for user ${ws.user?.email}:`, error);
    });

    ws.on('pong', () => {
      ws.isAlive = true;
    });
  }

  /**
   * Handle messages from clients
   */
  private async handleClientMessage(ws: AuthenticatedWebSocket, message: WebSocketMessage): Promise<void> {
    console.log(`📨 Received message type: ${message.type} from user: ${ws.user?.email}`);
    // console.log(`📨 Message data:`, JSON.stringify(message.data, null, 2));

    switch (message.type) {
      case 'start_chat':
        await this.handleStartChat(ws, message);
        break;

      case 'end_chat':
        await this.handleEndChat(ws, message);
        break;

      case 'audio_input':
        await this.handleAudioInput(ws, message);
        break;

      case 'text_input':
        await this.handleTextInput(ws, message);
        break;

      case 'ping':
        this.sendMessage(ws, {
          type: 'pong',
          data: { timestamp: new Date() },
          timestamp: new Date()
        });
        break;

      default:
        console.log(`🤷 Unhandled message type: ${message.type}`);
        this.sendError(ws, 'UNKNOWN_MESSAGE_TYPE', `Unknown message type: ${message.type}`);
    }
  }

  /**
   * Handle start chat request
   */
  private async handleStartChat(ws: AuthenticatedWebSocket, message: WebSocketMessage): Promise<void> {
    try {
      console.log(`🎬 Handling start chat request for user: ${ws.user?.email}`);

      // Clean up any existing connections first
      if (ws.session || ws.humeSocket) {
        console.log(`🧹 Cleaning up existing connections for user: ${ws.user?.email}`);
        await this.cleanupClientConnections(ws);
      }

      const { resumedChatGroupId } = message.data || {};
      console.log(`🔄 Creating Hume EVI connection for user: ${ws.user?.email}, resumedChatGroupId: ${resumedChatGroupId}`);

      // Create Hume EVI connection
      const { socket: humeSocket, session } = await this.humeService.createChatConnection(
        ws.user!.id,
        resumedChatGroupId
      );

      ws.session = session;
      ws.humeSocket = humeSocket;

      // Set up Hume socket handlers to forward messages to client
      this.setupHumeSocketHandlers(ws, humeSocket);

      // Log socket state for debugging
      const socket = (humeSocket as any).socket;
      console.log(`🔍 Hume socket state after creation:`, {
        hasSocket: !!socket,
        readyState: socket ? socket.readyState : 'no socket',
        OPEN: socket ? socket.constructor.OPEN : 'unknown',
        isOpen: socket ? socket.readyState === socket.constructor.OPEN : false
      });

      // Wait a moment for the Hume socket to be fully ready
      await new Promise(resolve => setTimeout(resolve, 100));

      // Check socket state again after waiting
      console.log(`🔍 Hume socket state after wait:`, {
        hasSocket: !!socket,
        readyState: socket ? socket.readyState : 'no socket',
        OPEN: socket ? socket.constructor.OPEN : 'unknown',
        isOpen: socket ? socket.readyState === socket.constructor.OPEN : false
      });

      this.sendMessage(ws, {
        type: 'chat_started',
        data: {
          sessionId: session.id,
          humeChatGroupId: session.humeChatGroupId
        },
        timestamp: new Date()
      });

      console.log(`✅ Chat started for user ${ws.user!.email}, session: ${session.id}`);

    } catch (error) {
      console.error('❌ Error starting chat:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      this.sendError(ws, 'CHAT_START_ERROR', 'Failed to start chat session');
    }
  }

  /**
   * Handle end chat request
   */
  private async handleEndChat(ws: AuthenticatedWebSocket, message: WebSocketMessage): Promise<void> {
    try {
      if (!ws.session || !ws.humeSocket) {
        this.sendError(ws, 'NO_ACTIVE_CHAT', 'No active chat session to end');
        return;
      }

      const { reason = 'user_ended' } = message.data || {};

      // Close Hume connection
      ws.humeSocket.close();

      // Map reason to valid database status
      const dbStatus: 'completed' | 'error' | 'interrupted' =
        reason === 'error' ? 'error' :
        reason === 'interrupted' ? 'interrupted' :
        'completed'; // Default to 'completed' for user_ended and other reasons

      // End session in database
      await this.humeService.endSession(ws.session.id, dbStatus);

      this.sendMessage(ws, {
        type: 'chat_ended',
        data: {
          sessionId: ws.session.id,
          reason
        },
        timestamp: new Date()
      });

      // Clean up
      delete ws.session;
      delete ws.humeSocket;

      console.log(`✅ Chat ended for user ${ws.user!.email}, reason: ${reason}`);

    } catch (error) {
      console.error('Error ending chat:', error);
      this.sendError(ws, 'CHAT_END_ERROR', 'Failed to end chat session');
    }
  }

  /**
   * Handle audio input from client
   */
  private async handleAudioInput(ws: AuthenticatedWebSocket, message: WebSocketMessage): Promise<void> {
    try {
      console.log(`🎙️ Received audio input from user: ${ws.user?.email}`);

      if (!ws.humeSocket) {
        console.error('❌ No active Hume socket for audio input');
        this.sendError(ws, 'NO_ACTIVE_CHAT', 'No active chat session for audio input');
        return;
      }

      // Enhanced socket state checking
      const socket = (ws.humeSocket as any).socket;
      const isSocketReady = socket && socket.readyState === socket.constructor.OPEN;

      console.log(`🎙️ Hume socket status:`, {
        hasHumeSocket: !!ws.humeSocket,
        socketExists: !!socket,
        readyState: socket?.readyState,
        expectedOpenState: socket?.constructor.OPEN,
        isReady: isSocketReady,
        hasSendMethod: typeof ws.humeSocket.sendAudioInput === 'function'
      });

      if (!isSocketReady) {
        console.error('❌ Hume socket not in OPEN state');
        this.sendError(ws, 'HUME_SOCKET_NOT_READY', 'Hume connection not ready. Please restart the chat.');
        return;
      }

      // Check if Hume socket has the required method
      if (typeof ws.humeSocket.sendAudioInput !== 'function') {
        console.error('❌ Hume socket missing sendAudioInput method');
        this.sendError(ws, 'HUME_METHOD_MISSING', 'Hume connection invalid. Please restart the chat.');
        return;
      }

      const { data: audioData } = message.data || {};

      if (!audioData) {
        console.error('❌ No audio data provided');
        this.sendError(ws, 'INVALID_AUDIO_DATA', 'Audio data is required');
        return;
      }

      console.log(`🎙️ Forwarding audio data to Hume, length: ${audioData.length}`);

      // Forward audio to Hume with additional error handling
      try {
        ws.humeSocket.sendAudioInput({ data: audioData });
        console.log(`✅ Audio data forwarded to Hume successfully`);
      } catch (sendError) {
        console.error('❌ Error sending audio to Hume:', sendError);
        throw sendError; // Re-throw to be caught by outer try-catch
      }

      // Capture user audio for storage (non-blocking background process)
      if (ws.session && ws.user) {
        try {
          // Queue user audio for storage without blocking the real-time flow
          // No consent check needed - voice storage is always enabled
          voiceProcessorService.processUserAudio({
            // messageId is optional for raw audio chunks - will be generated during processing
            userId: ws.user.id,
            sessionId: ws.session.id,
            role: 'user',
            audioData: audioData,
            mimeType: 'audio/webm', // Default MIME type, could be detected from data
            timestamp: new Date(),
            metadata: {
              source: 'websocket',
              userAgent: message.data?.userAgent || 'unknown',
              audioChunkId: `user_audio_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
            }
          }).catch(error => {
            // Log error but don't affect the real-time conversation
            console.error('⚠️ Error queuing user audio for storage:', error);
          });
        } catch (error) {
          // Silently handle any errors to avoid affecting real-time conversation
          console.error('⚠️ Error processing user audio for storage:', error);
        }
      }

    } catch (error) {
      console.error('❌ Error handling audio input:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        errorType: error?.constructor?.name
      });

      // Provide more specific error messages
      let errorCode = 'AUDIO_INPUT_ERROR';
      let errorMessage = 'Failed to process audio input';

      if (error instanceof Error) {
        if (error.message.includes('Socket is not connected')) {
          errorCode = 'HUME_SOCKET_DISCONNECTED';
          errorMessage = 'Hume connection lost. Please restart the chat.';
        } else if (error.message.includes('Socket is not open')) {
          errorCode = 'HUME_SOCKET_NOT_OPEN';
          errorMessage = 'Hume connection not ready. Please wait and try again.';
        } else {
          errorMessage = `Audio processing failed: ${error.message}`;
        }
      }

      this.sendError(ws, errorCode, errorMessage);
    }
  }

  /**
   * Handle text input from client (if supported)
   */
  private async handleTextInput(ws: AuthenticatedWebSocket, message: WebSocketMessage): Promise<void> {
    try {
      if (!ws.humeSocket) {
        this.sendError(ws, 'NO_ACTIVE_CHAT', 'No active chat session for text input');
        return;
      }

      const { text } = message.data || {};
      
      if (!text) {
        this.sendError(ws, 'INVALID_TEXT_DATA', 'Text data is required');
        return;
      }

      // Forward text to Hume (if your configuration supports it)
      if (ws.humeSocket && typeof (ws.humeSocket as any).sendTextInput === 'function') {
        (ws.humeSocket as any).sendTextInput({ text });
      }

    } catch (error) {
      console.error('Error handling text input:', error);
      this.sendError(ws, 'TEXT_INPUT_ERROR', 'Failed to process text input');
    }
  }

  /**
   * Set up handlers for Hume socket to forward messages to client
   */
  private setupHumeSocketHandlers(ws: AuthenticatedWebSocket, humeSocket: Hume.empathicVoice.chat.ChatSocket): void {
    // Store reference to this for use in the callback
    const self = this;

    humeSocket.on('message', async (humeMessage: Hume.empathicVoice.SubscribeEvent) => {
      console.log(`🔄 Forwarding Hume message type: ${humeMessage.type} to client`);

      // Forward Hume messages to client
      self.sendMessage(ws, {
        type: 'hume_message',
        data: humeMessage,
        timestamp: new Date()
      });

      // IMPORTANT: Also process the message for database storage
      // This ensures that messages are both forwarded to client AND stored in database
      if (ws.session) {
        try {
          console.log(`💾 Processing Hume message for database storage: ${humeMessage.type}`);
          console.log(`🔍 Debug: humeService exists:`, !!self.humeService);
          console.log(`🔍 Debug: processHumeMessage exists:`, !!self.humeService?.processHumeMessage);

          if (!self.humeService) {
            console.error(`❌ humeService is undefined!`);
            return;
          }

          if (!self.humeService.processHumeMessage) {
            console.error(`❌ processHumeMessage method is undefined!`);
            return;
          }

          await self.humeService.processHumeMessage(humeMessage, ws.session);
          console.log(`✅ Message processed successfully for database storage`);
        } catch (error) {
          console.error(`❌ Error processing Hume message for database:`, error);
        }
      }
    });

    humeSocket.on('error', (error: Error) => {
      console.error(`❌ Hume socket error:`, error);
      this.sendError(ws, 'HUME_ERROR', error.message);
    });

    humeSocket.on('close', () => {
      console.log(`🔌 Hume socket closed`);
      this.sendMessage(ws, {
        type: 'hume_disconnected',
        data: { reason: 'Hume connection closed' },
        timestamp: new Date()
      });
    });
  }

  /**
   * Clean up client connections (Hume socket and session)
   */
  private async cleanupClientConnections(ws: AuthenticatedWebSocket): Promise<void> {
    try {
      console.log(`🧹 Cleaning up connections for user: ${ws.user?.email}`);

      // Close Hume socket if exists
      if (ws.humeSocket) {
        try {
          console.log(`🔌 Closing Hume socket for user: ${ws.user?.email}`);
          const socket = (ws.humeSocket as any).socket;
          if (socket && socket.readyState !== socket.constructor.CLOSED) {
            ws.humeSocket.close();
          }
        } catch (error) {
          console.error('Error closing Hume socket:', error);
        }
        ws.humeSocket = undefined;
      }

      // Update session status if exists
      if (ws.session) {
        try {
          console.log(`💾 Updating session status to ended for session: ${ws.session.id}`);
          await this.humeService.endSession(ws.session.id, 'interrupted');
        } catch (error) {
          console.error('Error updating session status:', error);
        }
        ws.session = undefined;
      }

    } catch (error) {
      console.error('Error cleaning up client connections:', error);
    }
  }

  /**
   * Handle client disconnect
   */
  private async handleClientDisconnect(ws: AuthenticatedWebSocket): Promise<void> {
    try {
      console.log(`🔌 Handling client disconnect for user: ${ws.user?.email}`);
      await this.cleanupClientConnections(ws);
    } catch (error) {
      console.error('Error handling client disconnect:', error);
    }
  }

  /**
   * Send message to client
   */
  private sendMessage(ws: AuthenticatedWebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * Send error message to client
   */
  private sendError(ws: AuthenticatedWebSocket, code: string, message: string): void {
    this.sendMessage(ws, {
      type: 'error',
      data: {
        code,
        message
      },
      timestamp: new Date()
    });
  }

  /**
   * Broadcast message to all connected clients
   */
  public broadcast(message: WebSocketMessage): void {
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
  }

  /**
   * Start heartbeat to detect dead connections
   */
  private startHeartbeat(): void {
    setInterval(() => {
      this.clients.forEach(ws => {
        if (!ws.isAlive) {
          console.log(`💀 Terminating dead connection for user: ${ws.user?.email}`);
          ws.terminate();
          this.clients.delete(ws);
          return;
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // 30 seconds
  }

  /**
   * Get connection statistics
   */
  public getStats(): {
    totalConnections: number;
    activeChats: number;
    connectedUsers: string[];
  } {
    const activeChats = Array.from(this.clients).filter(ws => ws.session).length;
    const connectedUsers = Array.from(this.clients)
      .map(ws => ws.user?.email)
      .filter(Boolean) as string[];

    return {
      totalConnections: this.clients.size,
      activeChats,
      connectedUsers
    };
  }

  /**
   * Close server
   */
  public close(): void {
    this.wss.close();
    console.log('🔌 Chat WebSocket server closed');
  }
}
