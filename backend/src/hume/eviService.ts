import { HumeClient } from 'hume';
import type { <PERSON> } from 'hume';
import { ChatSessionModel } from '../database/models/ChatSession.js';
import { ConversationMessageModel } from '../database/models/ConversationMessage.js';
import { voiceProcessorService } from '../services/voiceProcessor.js';
import * as SharedTypes from '../../../shared/types';
const { HumeError } = SharedTypes;
// import { HumeError } from '../../../shared/types.ts';
import type { ChatSession, ConversationMessage, EmotionScores, HumeConfig } from '../../../shared/types.js';


export class HumeEVIService {
  private client: HumeClient;
  private config: HumeConfig;

  constructor() {
    const apiKey = process.env.HUME_API_KEY!.trim();
    const secretKey = process.env.HUME_SECRET_KEY?.trim();
    const configId = process.env.HUME_CONFIG_ID?.trim();
    const baseUrl = process.env.HUME_BASE_URL || 'https://api.hume.ai';

    this.config = {
      apiKey,
      baseUrl
    };

    if (secretKey) {
      this.config.secretKey = secretKey;
    }

    if (configId) {
      this.config.configId = configId;
    }

    if (!apiKey) {
      throw new Error('HUME_API_KEY environment variable is required');
    }

    const clientOptions: any = { apiKey };
    if (secretKey) {
      clientOptions.secretKey = secretKey;
    }

    this.client = new HumeClient(clientOptions);
  }

  /**
   * Create a new EVI chat connection for a user
   */
  async createChatConnection(userId: string, resumedChatGroupId?: string): Promise<{
    socket: Hume.empathicVoice.chat.ChatSocket;
    session: ChatSession;
  }> {
    try {
      console.log(`🔗 Creating Hume chat connection for user: ${userId}`);

      // Create database session first
      const sessionData: any = { userId };

      if (resumedChatGroupId) {
        sessionData.humeChatGroupId = resumedChatGroupId;
        console.log(`🔄 Resuming chat group: ${resumedChatGroupId}`);
      }

      sessionData.metadata = {
        configId: this.config.configId,
        startedAt: new Date().toISOString()
      };

      console.log(`💾 Creating database session with config: ${this.config.configId}`);
      const session = await ChatSessionModel.create(sessionData);
      console.log(`✅ Database session created: ${session.id}`);

      // Create WebSocket connection to Hume
      const connectArgs: any = {};
      if (this.config.configId) {
        connectArgs.configId = this.config.configId;
      }
      if (resumedChatGroupId) {
        connectArgs.resumedChatGroupId = resumedChatGroupId;
      }

      console.log(`🌐 Connecting to Hume EVI with args:`, connectArgs);
      const socket = await this.client.empathicVoice.chat.connect(connectArgs);
      console.log(`✅ Hume EVI socket connected successfully`);

      // Wait for socket to be fully open
      await this.waitForSocketOpen(socket);
      console.log(`🔓 Hume EVI socket is now open and ready`);

      // Set up event handlers
      this.setupSocketHandlers(socket, session);
      console.log(`🎧 Event handlers set up for session: ${session.id}`);

      return { socket, session };

    } catch (error) {
      console.error('❌ Failed to create Hume EVI connection:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        config: this.config
      });
      throw new HumeError('Failed to create chat connection');
    }
  }

  /**
   * Wait for the Hume socket to be in OPEN state with enhanced error handling
   */
  private async waitForSocketOpen(socket: Hume.empathicVoice.chat.ChatSocket): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timeout waiting for Hume socket to open (10s)'));
      }, 10000); // 10 second timeout

      let checkCount = 0;
      const maxChecks = 200; // Prevent infinite checking

      const checkSocket = () => {
        checkCount++;
        const internalSocket = (socket as any).socket;

        if (!internalSocket) {
          if (checkCount >= maxChecks) {
            clearTimeout(timeout);
            reject(new Error('Hume socket internal socket not available'));
            return;
          }
          setTimeout(checkSocket, 50);
          return;
        }

        const readyState = internalSocket.readyState;
        const OPEN = internalSocket.constructor.OPEN;
        const CLOSED = internalSocket.constructor.CLOSED;
        const CLOSING = internalSocket.constructor.CLOSING;

        console.log(`🔍 Socket check ${checkCount}: readyState=${readyState}, OPEN=${OPEN}`);

        if (readyState === OPEN) {
          clearTimeout(timeout);
          console.log(`✅ Hume socket is now OPEN after ${checkCount} checks`);
          resolve();
        } else if (readyState === CLOSED || readyState === CLOSING) {
          clearTimeout(timeout);
          reject(new Error(`Hume socket closed during connection (state: ${readyState})`));
        } else if (checkCount >= maxChecks) {
          clearTimeout(timeout);
          reject(new Error(`Hume socket failed to open after ${maxChecks} checks`));
        } else {
          // Check again in 50ms
          setTimeout(checkSocket, 50);
        }
      };

      // Start checking immediately
      checkSocket();
    });
  }

  /**
   * Set up WebSocket event handlers for comprehensive data capture
   */
  private setupSocketHandlers(socket: Hume.empathicVoice.chat.ChatSocket, session: ChatSession): void {
    socket.on('open', () => {
      console.log(`🔗 EVI connection opened for session ${session.id}`);
    });

    // Note: Message handling is now done by the WebSocket server
    // to ensure both client forwarding and database storage happen

    socket.on('error', async (error: Error) => {
      console.error(`❌ EVI connection error for session ${session.id}:`, error);
      await ChatSessionModel.update(session.id, {
        status: 'error',
        metadata: {
          ...session.metadata,
          error: error.message,
          errorAt: new Date().toISOString()
        }
      });
    });

    socket.on('close', async () => {
      console.log(`🔌 EVI connection closed for session ${session.id}`);
      await this.endSession(session.id);
    });
  }

  /**
   * Public method to process Hume messages (called from WebSocket server)
   */
  public async processHumeMessage(
    message: Hume.empathicVoice.SubscribeEvent,
    session: ChatSession
  ): Promise<void> {
    await this.handleHumeMessage(message, session);
  }

  /**
   * Handle different types of messages from Hume EVI
   */
  private async handleHumeMessage(
    message: Hume.empathicVoice.SubscribeEvent,
    session: ChatSession
  ): Promise<void> {
    console.log(`📨 Received message type: ${message.type} for session ${session.id}`);

    // Enable full message logging to debug emotion extraction
    if (message.type === 'user_message') {
      console.log(`📨 Full user_message data:`, JSON.stringify(message, null, 2));
    }

    switch (message.type) {
      case 'chat_metadata':
        await this.handleChatMetadata(message, session);
        break;

      case 'user_message':
        await this.handleUserMessage(message, session);
        break;

      case 'assistant_message':
        await this.handleAssistantMessage(message, session);
        break;

      case 'audio_output':
        await this.handleAudioOutput(message, session);
        break;

      case 'user_interruption':
        await this.handleUserInterruption(message, session);
        break;

      case 'tool_call':
        await this.handleToolCall(message, session);
        break;

      case 'error':
        await this.handleError(message as any, session);
        break;

      default:
        console.log(`🤷 Unhandled message type: ${message.type}`);
    }
  }

  /**
   * Handle chat metadata (includes chat group ID)
   */
  private async handleChatMetadata(
    message: Hume.empathicVoice.ChatMetadata, 
    session: ChatSession
  ): Promise<void> {
    await ChatSessionModel.update(session.id, {
      humeChatGroupId: message.chatGroupId,
      metadata: {
        ...session.metadata,
        chatMetadata: message
      }
    });
  }

  /**
   * Handle user messages with emotion analysis
   */
  private async handleUserMessage(
    message: Hume.empathicVoice.UserMessage,
    session: ChatSession
  ): Promise<void> {
    console.log(`👤 Processing user message for session ${session.id}`);
    console.log(`👤 Message content: "${message.message.content}"`);

    const emotions = this.extractEmotions(message);
    const prosodyScores = this.extractProsodyScores(message);

    try {
      const savedMessage = await ConversationMessageModel.create({
        sessionId: session.id,
        role: 'user',
        content: message.message.content || '',
        emotions,
        prosodyScores,
        metadata: {
          messageId: (message.message as any)?.id || undefined,
          timestamp: (message as any)?.timestamp || new Date(),
          models: (message as any)?.models
        }
      });
      console.log(`✅ User message saved with ID: ${savedMessage.id}`);
    } catch (error) {
      console.error(`❌ Error saving user message:`, error);
    }
  }

  /**
   * Handle assistant messages with emotion analysis
   */
  private async handleAssistantMessage(
    message: Hume.empathicVoice.AssistantMessage,
    session: ChatSession
  ): Promise<void> {
    console.log(`🤖 Processing assistant message for session ${session.id}`);
    console.log(`🤖 Message content: "${message.message.content}"`);

    // Extract emotions from assistant message (if available)
    const emotions = this.extractAssistantEmotions(message);
    const prosodyScores = this.extractAssistantProsodyScores(message);

    try {
      const savedMessage = await ConversationMessageModel.create({
        sessionId: session.id,
        role: 'assistant',
        content: message.message.content || '',
        emotions,
        prosodyScores,
        metadata: {
          messageId: (message.message as any)?.id || undefined,
          timestamp: (message as any)?.timestamp || new Date(),
          models: (message as any)?.models
        }
      });
      console.log(`✅ Assistant message saved with ID: ${savedMessage.id}`);
    } catch (error) {
      console.error(`❌ Error saving assistant message:`, error);
    }
  }

  /**
   * Handle audio output and store assistant voice data
   */
  private async handleAudioOutput(
    message: Hume.empathicVoice.AudioOutput,
    session: ChatSession
  ): Promise<void> {
    console.log(`🔊 Audio output received for session ${session.id}`);

    // Update session metadata with audio info
    const currentMetadata = session.metadata || {};
    const audioCount = ((currentMetadata as any).audioOutputCount || 0) + 1;

    await ChatSessionModel.update(session.id, {
      metadata: {
        ...currentMetadata,
        audioOutputCount: audioCount,
        lastAudioAt: new Date().toISOString()
      }
    });

    // Capture assistant audio for storage (non-blocking background process)
    if (message.data && session.userId) {
      try {
        // Queue assistant audio for storage without blocking the real-time flow
        // No consent check needed - voice storage is always enabled
        voiceProcessorService.processAssistantAudio({
          // messageId is optional for raw audio chunks - will be generated during processing
          userId: session.userId,
          sessionId: session.id,
          role: 'assistant',
          audioData: message.data, // Hume provides base64 encoded audio
          mimeType: 'audio/mp3', // Hume typically provides MP3 audio
          timestamp: new Date(),
          metadata: {
            source: 'hume_evi',
            humeMessageId: (message as any)?.id || undefined,
            audioOutputCount: audioCount,
            audioChunkId: `assistant_audio_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
          }
        }).catch(error => {
          // Log error but don't affect the real-time conversation
          console.error('⚠️ Error queuing assistant audio for storage:', error);
        });
      } catch (error) {
        // Silently handle any errors to avoid affecting real-time conversation
        console.error('⚠️ Error processing assistant audio for storage:', error);
      }
    }
  }

  /**
   * Handle user interruptions
   */
  private async handleUserInterruption(
    message: Hume.empathicVoice.UserInterruption, 
    session: ChatSession
  ): Promise<void> {
    const currentMetadata = session.metadata || {};
    const interruptionCount = ((currentMetadata as any).interruptionCount || 0) + 1;
    
    await ChatSessionModel.update(session.id, {
      metadata: {
        ...currentMetadata,
        interruptionCount,
        lastInterruptionAt: new Date().toISOString()
      }
    });
  }

  /**
   * Handle tool calls (function calling)
   */
  private async handleToolCall(
    message: Hume.empathicVoice.ToolCallMessage, 
    session: ChatSession
  ): Promise<void> {
    console.log(`🔧 Tool call received: ${message.name} for session ${session.id}`);
    
    // Store tool call information
    await ConversationMessageModel.create({
      sessionId: session.id,
      role: 'assistant',
      content: `[Tool Call: ${message.name}]`,
      metadata: {
        toolCall: {
          name: message.name,
          parameters: message.parameters,
          toolCallId: message.toolCallId
        },
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle errors from Hume
   */
  private async handleError(
    message: Hume.empathicVoice.ToolErrorMessage,
    session: ChatSession
  ): Promise<void> {
    console.error(`❌ Hume error for session ${session.id}:`, message);
    
    await ChatSessionModel.update(session.id, {
      status: 'error',
      metadata: {
        ...session.metadata,
        humeError: {
          code: (message as any).code,
          message: (message as any).message,
          slug: (message as any).slug,
          timestamp: new Date().toISOString()
        }
      }
    });
  }

  /**
   * Extract emotion scores from message - Enhanced to capture all emotion data
   */
  private extractEmotions(message: Hume.empathicVoice.UserMessage): EmotionScores {
    const emotions: EmotionScores = {};

    // console.log(`🔍 Extracting emotions from message models:`, JSON.stringify(message.models, null, 2));

    // Extract from prosody scores (voice-based emotions)
    if (message.models?.prosody?.scores) {
      console.log(`🎵 Found prosody scores:`, Object.keys(message.models.prosody.scores));
      Object.entries(message.models.prosody.scores).forEach(([emotion, score]) => {
        emotions[emotion] = score;
      });
    }

    // Use flexible access to check for other emotion sources
    const models = message.models as any;

    // Extract from language/utterance-based emotions if available
    if (models?.language?.scores) {
      console.log(`💬 Found language scores:`, Object.keys(models.language.scores));
      Object.entries(models.language.scores).forEach(([emotion, score]) => {
        const numScore = score as number;
        // Combine with prosody scores, giving slight preference to language-based emotions
        emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore * 1.1) / 2 : numScore;
      });
    }

    // Check for face-based emotions
    if (models?.face?.scores) {
      console.log(`😊 Found face scores:`, Object.keys(models.face.scores));
      Object.entries(models.face.scores).forEach(([emotion, score]) => {
        const numScore = score as number;
        emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore) / 2 : numScore;
      });
    }

    // Check for any other emotion model types
    if (models) {
      Object.keys(models).forEach(modelType => {
        if (modelType !== 'prosody' && modelType !== 'language' && modelType !== 'face') {
          const modelData = models[modelType];
          if (modelData?.scores) {
            console.log(`🔍 Found ${modelType} scores:`, Object.keys(modelData.scores));
            Object.entries(modelData.scores).forEach(([emotion, score]) => {
              const numScore = score as number;
              emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore) / 2 : numScore;
            });
          }
        }
      });
    }

    console.log(`✅ Final extracted emotions:`, Object.keys(emotions).length, 'emotions found');
    console.log(`🎯 Top emotions:`, Object.entries(emotions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([emotion, score]) => `${emotion}: ${score.toFixed(3)}`)
      .join(', '));

    return emotions;
  }

  /**
   * Extract prosody scores from message
   */
  private extractProsodyScores(message: Hume.empathicVoice.UserMessage): Record<string, number> {
    return (message.models?.prosody?.scores as unknown as Record<string, number>) || {};
  }

  /**
   * Extract emotion scores from assistant message - Enhanced to capture all emotion data
   */
  private extractAssistantEmotions(message: Hume.empathicVoice.AssistantMessage): EmotionScores {
    const emotions: EmotionScores = {};

    console.log(`🤖 Extracting assistant emotions from message models:`, message.models ? Object.keys(message.models) : 'no models');

    // Extract from prosody scores (voice-based emotions)
    if (message.models?.prosody?.scores) {
      console.log(`🎵 Found assistant prosody scores:`, Object.keys(message.models.prosody.scores));
      Object.entries(message.models.prosody.scores).forEach(([emotion, score]) => {
        emotions[emotion] = score;
      });
    }

    // Use flexible access to check for other emotion sources
    const models = message.models as any;

    // Extract from language/utterance-based emotions if available
    if (models?.language?.scores) {
      console.log(`💬 Found assistant language scores:`, Object.keys(models.language.scores));
      Object.entries(models.language.scores).forEach(([emotion, score]) => {
        const numScore = score as number;
        // Combine with prosody scores, giving slight preference to language-based emotions
        emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore * 1.1) / 2 : numScore;
      });
    }

    // Check for face-based emotions
    if (models?.face?.scores) {
      console.log(`😊 Found assistant face scores:`, Object.keys(models.face.scores));
      Object.entries(models.face.scores).forEach(([emotion, score]) => {
        const numScore = score as number;
        emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore) / 2 : numScore;
      });
    }

    // Check for any other emotion model types
    if (models) {
      Object.keys(models).forEach(modelType => {
        if (modelType !== 'prosody' && modelType !== 'language' && modelType !== 'face') {
          const modelData = models[modelType];
          if (modelData?.scores) {
            console.log(`🔍 Found assistant ${modelType} scores:`, Object.keys(modelData.scores));
            Object.entries(modelData.scores).forEach(([emotion, score]) => {
              const numScore = score as number;
              emotions[emotion] = emotions[emotion] ? (emotions[emotion] + numScore) / 2 : numScore;
            });
          }
        }
      });
    }

    console.log(`✅ Final assistant emotions:`, Object.keys(emotions).length, 'emotions found');
    console.log(`🎯 Top assistant emotions:`, Object.entries(emotions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([emotion, score]) => `${emotion}: ${score.toFixed(3)}`)
      .join(', '));

    return emotions;
  }

  /**
   * Extract prosody scores from assistant message
   */
  private extractAssistantProsodyScores(message: Hume.empathicVoice.AssistantMessage): Record<string, number> {
    return (message.models?.prosody?.scores as unknown as Record<string, number>) || {};
  }

  /**
   * End a chat session
   */
  async endSession(sessionId: string, reason: 'completed' | 'error' | 'interrupted' = 'completed'): Promise<void> {
    try {
      const session = await ChatSessionModel.findById(sessionId);
      if (!session) return;

      // Calculate session statistics
      const stats = await ChatSessionModel.getSessionStats(sessionId);
      
      await ChatSessionModel.update(sessionId, {
        status: reason,
        endedAt: new Date(),
        metadata: {
          ...session.metadata,
          endReason: reason,
          finalStats: stats,
          endedAt: new Date().toISOString()
        }
      });

      console.log(`✅ Session ${sessionId} ended with reason: ${reason}`);
    } catch (error) {
      console.error(`Failed to end session ${sessionId}:`, error);
    }
  }

  /**
   * Get session analytics
   */
  async getSessionAnalytics(sessionId: string): Promise<{
    session: ChatSession | null;
    messages: ConversationMessage[];
    stats: any;
    emotionAnalytics: any;
  }> {
    const session = await ChatSessionModel.findById(sessionId);
    const messages = await ConversationMessageModel.findBySessionId(sessionId);
    const stats = await ChatSessionModel.getSessionStats(sessionId);
    const emotionAnalytics = await ConversationMessageModel.getEmotionAnalytics(sessionId);

    return {
      session,
      messages,
      stats,
      emotionAnalytics
    };
  }
}
