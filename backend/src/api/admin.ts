import { Router, Request, Response } from 'express';
import { authMiddleware } from '../middleware/auth.js';
import { requireAdmin, requireAdminManagement, requireDataExport } from '../middleware/adminAuth.js';
import { adminAnalyticsService } from '../services/adminAnalytics.js';
import { advancedAnalyticsService } from '../services/advancedAnalytics.js';
import { AdminWhitelistModel } from '../database/models/AdminWhitelist.js';
import { AdminActivityLogModel } from '../database/models/AdminActivityLog.js';
import { query } from '../database/config.js';
import type { ApiResponse } from '../../../shared/types.js';

const router: Router = Router();

// Apply authentication middleware to all admin routes
router.use(authMiddleware.authenticate);

/**
 * GET /admin/analytics
 * Get comprehensive admin analytics
 */
router.get('/analytics', requireAdmin({
  action: 'view_analytics',
  resource: 'admin_analytics'
}), async (req: Request, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    
    if (days < 1 || days > 365) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_DAYS_PARAMETER',
          message: 'Days parameter must be between 1 and 365'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const analytics = await adminAnalyticsService.getAdminAnalytics(days);

    return res.json({
      success: true,
      data: analytics,
      timestamp: new Date()
    } as ApiResponse<typeof analytics>);

  } catch (error) {
    console.error('Error fetching admin analytics:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'ADMIN_ANALYTICS_ERROR',
        message: 'Failed to fetch admin analytics'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/users
 * Get user management data
 */
router.get('/users', requireAdmin({
  action: 'view_users',
  resource: 'user_management'
}), async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
    const offset = (page - 1) * limit;

    // Get users with basic stats
    const usersResult = await query(`
      SELECT 
        u.id,
        u.name,
        u.email,
        u.created_at,
        u.profile_data->>'onboardingCompleted' as onboarding_completed,
        u.profile_data->>'personaType' as persona_type,
        COUNT(cs.id) as total_sessions,
        MAX(cs.started_at) as last_session_at
      FROM users u
      LEFT JOIN chat_sessions cs ON u.id = cs.user_id
      GROUP BY u.id, u.name, u.email, u.created_at, u.profile_data
      ORDER BY u.created_at DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    // Get total count
    const countResult = await query('SELECT COUNT(*) as total FROM users');
    const totalUsers = parseInt(countResult.rows[0].total);

    const users = usersResult.rows.map((row: any) => ({
      id: row.id,
      name: row.name,
      email: row.email,
      createdAt: row.created_at,
      onboardingCompleted: row.onboarding_completed === 'true',
      personaType: row.persona_type,
      totalSessions: parseInt(row.total_sessions) || 0,
      lastSessionAt: row.last_session_at
    }));

    return res.json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total: totalUsers,
          totalPages: Math.ceil(totalUsers / limit)
        }
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching admin users:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'ADMIN_USERS_ERROR',
        message: 'Failed to fetch user data'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/sessions
 * Get session management data
 */
router.get('/sessions', requireAdmin, async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
    const offset = (page - 1) * limit;
    const status = req.query.status as string;

    let whereClause = '';
    const params: any[] = [limit, offset];
    
    if (status && ['active', 'completed', 'error', 'interrupted'].includes(status)) {
      whereClause = 'WHERE cs.status = $3';
      params.push(status);
    }

    const sessionsResult = await query(`
      SELECT 
        cs.id,
        cs.user_id,
        u.name as user_name,
        u.email as user_email,
        cs.started_at,
        cs.ended_at,
        cs.status,
        EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))/60 as duration_minutes,
        COUNT(cm.id) as message_count
      FROM chat_sessions cs
      JOIN users u ON cs.user_id = u.id
      LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
      ${whereClause}
      GROUP BY cs.id, cs.user_id, u.name, u.email, cs.started_at, cs.ended_at, cs.status
      ORDER BY cs.started_at DESC
      LIMIT $1 OFFSET $2
    `, params);

    const sessions = sessionsResult.rows.map((row: any) => ({
      id: row.id,
      userId: row.user_id,
      userName: row.user_name,
      userEmail: row.user_email,
      startedAt: row.started_at,
      endedAt: row.ended_at,
      status: row.status,
      durationMinutes: parseFloat(row.duration_minutes) || 0,
      messageCount: parseInt(row.message_count) || 0
    }));

    return res.json({
      success: true,
      data: { sessions },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching admin sessions:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'ADMIN_SESSIONS_ERROR',
        message: 'Failed to fetch session data'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/whitelist
 * Get admin whitelist entries
 */
router.get('/whitelist', requireAdminManagement, async (req: Request, res: Response) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const admins = await AdminWhitelistModel.getAll(includeInactive);
    const stats = await AdminWhitelistModel.getStats();

    return res.json({
      success: true,
      data: {
        admins,
        stats
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching admin whitelist:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'ADMIN_WHITELIST_ERROR',
        message: 'Failed to fetch admin whitelist'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * POST /admin/whitelist
 * Add new admin to whitelist
 */
router.post('/whitelist', requireAdminManagement, async (req: Request, res: Response) => {
  try {
    const { email, role, permissions, notes } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_EMAIL',
          message: 'Email is required'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const adminData: any = {
      email,
      role: role || 'admin',
      permissions: permissions || {}
    };

    if (req.user?.id) adminData.addedBy = req.user.id;
    if (notes) adminData.notes = notes;

    const newAdmin = await AdminWhitelistModel.add(adminData);

    return res.json({
      success: true,
      data: { admin: newAdmin },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error: any) {
    console.error('Error adding admin to whitelist:', error);

    if (error?.code === '23505') { // Unique constraint violation
      return res.status(409).json({
        success: false,
        error: {
          code: 'EMAIL_ALREADY_EXISTS',
          message: 'Email already exists in whitelist'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    return res.status(500).json({
      success: false,
      error: {
        code: 'ADMIN_WHITELIST_ADD_ERROR',
        message: 'Failed to add admin to whitelist'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * PUT /admin/whitelist/:email
 * Update admin whitelist entry
 */
router.put('/whitelist/:email', requireAdminManagement, async (req: Request, res: Response) => {
  try {
    const { email } = req.params;
    if (!email) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_EMAIL',
          message: 'Email parameter is required'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const { role, isActive, permissions, notes } = req.body;

    const updatedAdmin = await AdminWhitelistModel.update(email, {
      role,
      isActive,
      permissions,
      notes
    });

    if (!updatedAdmin) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ADMIN_NOT_FOUND',
          message: 'Admin not found in whitelist'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    return res.json({
      success: true,
      data: { admin: updatedAdmin },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error updating admin whitelist:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'ADMIN_WHITELIST_UPDATE_ERROR',
        message: 'Failed to update admin whitelist'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * DELETE /admin/whitelist/:email
 * Remove admin from whitelist
 */
router.delete('/whitelist/:email', requireAdminManagement, async (req: Request, res: Response) => {
  try {
    const { email } = req.params;
    if (!email) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_EMAIL',
          message: 'Email parameter is required'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    // Prevent removing yourself
    if (email.toLowerCase() === req.user?.email?.toLowerCase()) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'CANNOT_REMOVE_SELF',
          message: 'Cannot remove yourself from whitelist'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const removed = await AdminWhitelistModel.remove(email);

    if (!removed) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ADMIN_NOT_FOUND',
          message: 'Admin not found in whitelist'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    return res.json({
      success: true,
      data: { message: 'Admin removed from whitelist' },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error removing admin from whitelist:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'ADMIN_WHITELIST_REMOVE_ERROR',
        message: 'Failed to remove admin from whitelist'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/activity-log
 * Get admin activity logs
 */
router.get('/activity-log', requireAdmin({
  action: 'view_activity_log',
  resource: 'admin_activity'
}), async (req: Request, res: Response) => {
  try {
    const {
      adminEmail,
      action,
      resource,
      startDate,
      endDate,
      page = 1,
      limit = 50
    } = req.query;

    const filter: any = {
      limit: Math.min(parseInt(limit as string) || 50, 100),
      offset: (parseInt(page as string) - 1) * (parseInt(limit as string) || 50)
    };

    if (adminEmail) filter.adminEmail = adminEmail as string;
    if (action) filter.action = action as string;
    if (resource) filter.resource = resource as string;
    if (startDate) filter.startDate = new Date(startDate as string);
    if (endDate) filter.endDate = new Date(endDate as string);

    const { activities, total } = await AdminActivityLogModel.getActivities(filter);
    const stats = await AdminActivityLogModel.getStats();

    return res.json({
      success: true,
      data: {
        activities,
        pagination: {
          page: parseInt(page as string) || 1,
          limit: filter.limit,
          total,
          totalPages: Math.ceil(total / filter.limit)
        },
        stats
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching admin activity log:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'ADMIN_ACTIVITY_LOG_ERROR',
        message: 'Failed to fetch admin activity log'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/system/health
 * Get system health status
 */
router.get('/system/health', requireAdmin({
  action: 'view_system_health',
  resource: 'system_health'
}), async (req: Request, res: Response) => {
  try {
    // Database health check
    const dbHealthResult = await query('SELECT NOW() as current_time');
    const dbHealthy = dbHealthResult.rows.length > 0;

    // Recent error check
    const recentErrorsResult = await query(`
      SELECT COUNT(*) as error_count
      FROM chat_sessions 
      WHERE status = 'error' 
      AND started_at >= NOW() - INTERVAL '1 hour'
    `);
    const recentErrors = parseInt(recentErrorsResult.rows[0].error_count);

    const overallHealth = dbHealthy && recentErrors < 5;

    return res.json({
      success: true,
      data: {
        overall: overallHealth ? 'healthy' : 'degraded',
        database: dbHealthy ? 'healthy' : 'unhealthy',
        recentErrors,
        timestamp: new Date()
      },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error checking system health:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'SYSTEM_HEALTH_ERROR',
        message: 'Failed to check system health'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/analytics/emotions/correlations
 * Get emotion correlation analysis
 */
router.get('/analytics/emotions/correlations', requireAdmin({
  action: 'view_emotion_correlations',
  resource: 'advanced_analytics'
}), async (req: Request, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    const correlations = await advancedAnalyticsService.getEmotionCorrelations(days);

    return res.json({
      success: true,
      data: { correlations },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching emotion correlations:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'EMOTION_CORRELATIONS_ERROR',
        message: 'Failed to fetch emotion correlations'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/analytics/cohorts
 * Get user cohort analysis
 */
router.get('/analytics/cohorts', requireAdmin({
  action: 'view_user_cohorts',
  resource: 'advanced_analytics'
}), async (req: Request, res: Response) => {
  try {
    const cohorts = await advancedAnalyticsService.getUserCohorts();

    return res.json({
      success: true,
      data: { cohorts },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching user cohorts:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'USER_COHORTS_ERROR',
        message: 'Failed to fetch user cohorts'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/analytics/anomalies
 * Get emotion anomaly detection
 */
router.get('/analytics/anomalies', requireAdmin({
  action: 'view_emotion_anomalies',
  resource: 'advanced_analytics'
}), async (req: Request, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 7;
    const anomalies = await advancedAnalyticsService.detectEmotionAnomalies(days);

    return res.json({
      success: true,
      data: { anomalies },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching emotion anomalies:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'EMOTION_ANOMALIES_ERROR',
        message: 'Failed to fetch emotion anomalies'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/analytics/predictions
 * Get predictive insights
 */
router.get('/analytics/predictions', requireAdmin({
  action: 'view_predictive_insights',
  resource: 'advanced_analytics'
}), async (req: Request, res: Response) => {
  try {
    const insights = await advancedAnalyticsService.getPredictiveInsights();

    return res.json({
      success: true,
      data: { insights },
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching predictive insights:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'PREDICTIVE_INSIGHTS_ERROR',
        message: 'Failed to fetch predictive insights'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/analytics/emotion-flow
 * Get real-time emotion flow analysis
 */
router.get('/analytics/emotion-flow', requireAdmin({
  action: 'view_emotion_flow',
  resource: 'advanced_analytics'
}), async (req: Request, res: Response) => {
  try {
    const hours = parseInt(req.query.hours as string) || 24;
    const emotionFlow = await advancedAnalyticsService.getEmotionFlowAnalysis(hours);

    return res.json({
      success: true,
      data: emotionFlow,
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching emotion flow analysis:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'EMOTION_FLOW_ERROR',
        message: 'Failed to fetch emotion flow analysis'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /admin/analytics/segmentation
 * Get user segmentation analysis
 */
router.get('/analytics/segmentation', requireAdmin({
  action: 'view_user_segmentation',
  resource: 'advanced_analytics'
}), async (req: Request, res: Response) => {
  try {
    const segmentation = await advancedAnalyticsService.getUserSegmentation();

    return res.json({
      success: true,
      data: segmentation,
      timestamp: new Date()
    } as ApiResponse);

  } catch (error) {
    console.error('Error fetching user segmentation:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'USER_SEGMENTATION_ERROR',
        message: 'Failed to fetch user segmentation'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

export default router;
