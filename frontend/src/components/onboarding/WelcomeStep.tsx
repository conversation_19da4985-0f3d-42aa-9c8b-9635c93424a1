import { useOnboarding } from '../../contexts/OnboardingContext'

export default function WelcomeStep() {
  const { nextStep } = useOnboarding()

  return (
    <div className="text-center space-y-12 max-w-md mx-auto">
      {/* ORA Logo */}
      <div className="flex justify-center mb-12">
        <img
          src="/ora_logo.svg"
          alt="ORA Logo"
          className="w-64 h-64 object-contain"
        />
      </div>

      {/* Welcome message - Ensure "Hey there, I'm <PERSON><PERSON>" stays on one line */}
      <div className="space-y-8">
        <h1 className="text-4xl md:text-5xl font-light text-white leading-tight tracking-wide whitespace-nowrap">
          Hey there, I'm Ora
        </h1>
        <p className="text-lg text-white/80 leading-relaxed font-light">
          Your personal AI friend there for you 24/7
        </p>
      </div>

      {/* Call to action button - Single button to start questionnaire */}
      <div className="pt-16">
        <button
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-aura-900 to-sunbeam-900 text-white py-5 px-8 rounded-3xl text-lg font-medium hover:from-aura-800 hover:to-sunbeam-800 transition-all duration-300 shadow-xl"
        >
          LET'S GET STARTED
        </button>
      </div>
    </div>
  )
}
