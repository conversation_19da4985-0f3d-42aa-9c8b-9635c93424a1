import { useEffect } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

/**
 * TokenHandler component that processes authentication tokens from URL parameters
 * This can be used on any page that might receive OAuth callback tokens
 */
export function TokenHandler() {
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  const { setUserFromTokens } = useAuth()

  useEffect(() => {
    const handleTokens = async () => {
      const token = searchParams.get('token')
      const userStr = searchParams.get('user')
      
      if (token && userStr) {
        try {
          const refreshToken = searchParams.get('refresh_token')
          const userData = JSON.parse(userStr)
          
          // Set tokens and user data
          await setUserFromTokens({
            accessToken: token,
            refreshToken: refreshToken || '',
            expiresIn: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
          }, userData)
          
          // Clean up URL parameters
          const newSearchParams = new URLSearchParams(searchParams)
          newSearchParams.delete('token')
          newSearchParams.delete('refresh_token')
          newSearchParams.delete('user')
          setSearchParams(newSearchParams, { replace: true })
          
          // Navigate based on user status
          const needsOnboarding = !userData.profileData?.onboardingCompleted
          if (needsOnboarding) {
            navigate('/onboarding', { replace: true })
          } else {
            navigate('/chat', { replace: true })
          }
        } catch (error) {
          console.error('Token processing error:', error)
          navigate('/auth/error?error=token_processing_failed', { replace: true })
        }
      }
    }

    handleTokens()
  }, [searchParams, setSearchParams, navigate, setUserFromTokens])

  return null // This component doesn't render anything
}
