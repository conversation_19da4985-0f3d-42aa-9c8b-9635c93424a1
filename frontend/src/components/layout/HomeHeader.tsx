import { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { User, Settings, LogOut, Menu, X } from 'lucide-react'

interface HomeHeaderProps {
  onProfileClick: () => void
  onSettingsClick: () => void
}

export default function HomeHeader({ onProfileClick, onSettingsClick }: HomeHeaderProps) {
  const { user, logout } = useAuth()
  const [showMenu, setShowMenu] = useState(false)

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const toggleMenu = () => {
    setShowMenu(!showMenu)
  }

  return (
    <header className="relative z-20">
      {/* Main Header */}
      <div className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <img
                src="/ora_logo.svg"
                alt="ORA Logo"
                className="w-16 h-16 object-contain"
              />
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-eclipse-950">ORA</h1>
                <p className="text-xs text-eclipse-950/60">Empathic AI Companion</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              {/* User Info */}
              <div className="flex items-center space-x-3 px-3 py-2 rounded-lg bg-white/20">
                <div className="w-8 h-8 bg-gradient-to-r from-aura-500 to-sunbeam-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {(user?.profileData?.firstName || user?.name)?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="text-left">
                  <p className="text-sm font-medium text-eclipse-950">
                    {user?.profileData?.firstName || user?.name?.split(' ')[0]}
                  </p>
                  <p className="text-xs text-eclipse-950/60">
                    {user?.profileData?.personaType || 'User'}
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <button
                onClick={onProfileClick}
                className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors duration-200"
                title="Profile"
              >
                <User className="w-5 h-5 text-eclipse-950" />
              </button>

              <button
                onClick={onSettingsClick}
                className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors duration-200"
                title="Settings"
              >
                <Settings className="w-5 h-5 text-eclipse-950" />
              </button>

              <button
                onClick={handleLogout}
                className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors duration-200"
                title="Logout"
              >
                <LogOut className="w-5 h-5 text-eclipse-950" />
              </button>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button
                onClick={toggleMenu}
                className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors duration-200"
              >
                {showMenu ? (
                  <X className="w-6 h-6 text-eclipse-950" />
                ) : (
                  <Menu className="w-6 h-6 text-eclipse-950" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {showMenu && (
        <div className="md:hidden absolute top-full left-0 right-0 z-10">
          <div className="bg-white/95 backdrop-blur-md border-b border-white/20 shadow-lg">
            <div className="px-4 py-4 space-y-4">
              {/* User Info */}
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-ardent-50 to-bliss-100">
                <div className="w-10 h-10 bg-gradient-to-r from-aura-500 to-sunbeam-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium">
                    {(user?.profileData?.firstName || user?.name)?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-eclipse-950">
                    {user?.profileData?.firstName || user?.name?.split(' ')[0]}
                  </p>
                  <p className="text-sm text-eclipse-950/60">
                    {user?.email}
                  </p>
                  {user?.profileData?.personaType && (
                    <p className="text-xs text-eclipse-950/50 capitalize">
                      {user.profileData.personaType} persona
                    </p>
                  )}
                </div>
              </div>

              {/* Menu Items */}
              <div className="space-y-2">
                <button
                  onClick={() => {
                    onProfileClick()
                    setShowMenu(false)
                  }}
                  className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-white/50 transition-colors duration-200"
                >
                  <User className="w-5 h-5 text-eclipse-950" />
                  <span className="text-eclipse-950 font-medium">Profile</span>
                </button>

                <button
                  onClick={() => {
                    onSettingsClick()
                    setShowMenu(false)
                  }}
                  className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-white/50 transition-colors duration-200"
                >
                  <Settings className="w-5 h-5 text-eclipse-950" />
                  <span className="text-eclipse-950 font-medium">Settings</span>
                </button>

                <button
                  onClick={() => {
                    handleLogout()
                    setShowMenu(false)
                  }}
                  className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-red-50 transition-colors duration-200 text-red-600"
                >
                  <LogOut className="w-5 h-5" />
                  <span className="font-medium">Sign Out</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
