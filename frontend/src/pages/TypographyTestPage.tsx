import React from 'react'

export default function TypographyTestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-ardent-50 via-bliss-100 to-sunbeam-100 p-8">
      <div className="max-w-4xl mx-auto space-y-12">
        
        {/* Page Header */}
        <div className="text-center space-y-4">
          <h1 className="text-5xl font-bold text-eclipse-950">
            ORA Typography System
          </h1>
          <p className="text-xl text-eclipse-950/80">
            Showcasing Georgia headings and Inter body text
          </p>
        </div>

        {/* Heading Examples */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-8 shadow-lg">
          <h2 className="text-3xl font-bold text-eclipse-950 mb-6">
            Heading Examples (Georgia Font)
          </h2>
          
          <div className="space-y-6">
            <div>
              <h1 className="text-4xl font-bold text-aura-900">H1 - Main Page Title</h1>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 2.25rem (36px), font-weight: bold</p>
            </div>
            
            <div>
              <h2 className="text-3xl font-semibold text-aura-800">H2 - Section Header</h2>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 1.875rem (30px), font-weight: semibold</p>
            </div>
            
            <div>
              <h3 className="text-2xl font-medium text-aura-700">H3 - Subsection Title</h3>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 1.5rem (24px), font-weight: medium</p>
            </div>
            
            <div>
              <h4 className="text-xl font-medium text-aura-600">H4 - Component Header</h4>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 1.25rem (20px), font-weight: medium</p>
            </div>
            
            <div>
              <h5 className="text-lg font-medium text-aura-500">H5 - Small Section</h5>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 1.125rem (18px), font-weight: medium</p>
            </div>
            
            <div>
              <h6 className="text-base font-medium text-aura-400">H6 - Minor Heading</h6>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 1rem (16px), font-weight: medium</p>
            </div>
          </div>
        </div>

        {/* Body Text Examples */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-8 shadow-lg">
          <h2 className="text-3xl font-bold text-eclipse-950 mb-6">
            Body Text Examples (Inter Font)
          </h2>
          
          <div className="space-y-6">
            <div>
              <p className="text-lg text-eclipse-950">
                Large body text - This is an example of larger body text using the Inter font family. 
                Inter provides excellent readability and a modern, clean appearance.
              </p>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 1.125rem (18px)</p>
            </div>
            
            <div>
              <p className="text-base text-eclipse-950">
                Regular body text - This is the standard body text size used throughout the ORA application. 
                The Inter font ensures optimal legibility across all devices and screen sizes.
              </p>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 1rem (16px)</p>
            </div>
            
            <div>
              <p className="text-sm text-eclipse-950">
                Small body text - Used for captions, metadata, and secondary information. 
                Even at smaller sizes, Inter maintains its clarity and professional appearance.
              </p>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 0.875rem (14px)</p>
            </div>
            
            <div>
              <p className="text-xs text-eclipse-950/80">
                Extra small text - Perfect for fine print, timestamps, and minimal UI elements.
              </p>
              <p className="text-sm text-eclipse-950/60 mt-1">font-size: 0.75rem (12px)</p>
            </div>
          </div>
        </div>

        {/* Interactive Elements */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-8 shadow-lg">
          <h2 className="text-3xl font-bold text-eclipse-950 mb-6">
            Interactive Elements
          </h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-medium text-eclipse-950 mb-3">Buttons</h3>
              <div className="flex flex-wrap gap-4">
                <button className="btn-primary">Primary Button</button>
                <button className="btn-secondary">Secondary Button</button>
                <button className="btn-outline">Outline Button</button>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-medium text-eclipse-950 mb-3">Form Elements</h3>
              <div className="space-y-4 max-w-md">
                <div>
                  <label className="block text-sm font-medium text-eclipse-950 mb-2">
                    Sample Input Field
                  </label>
                  <input 
                    type="text" 
                    placeholder="Enter text here..." 
                    className="input"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-eclipse-950 mb-2">
                    Sample Textarea
                  </label>
                  <textarea 
                    placeholder="Enter longer text here..." 
                    className="input h-24 resize-none"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Font Information */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-8 shadow-lg">
          <h2 className="text-3xl font-bold text-eclipse-950 mb-6">
            Font Implementation Details
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-medium text-eclipse-950 mb-4">Georgia (Headings)</h3>
              <ul className="space-y-2 text-eclipse-950">
                <li>• Classic serif typeface</li>
                <li>• Excellent for headings and titles</li>
                <li>• High readability on screens</li>
                <li>• Professional and elegant appearance</li>
                <li>• Applied to: h1, h2, h3, h4, h5, h6</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-xl font-medium text-eclipse-950 mb-4">Inter (Body Text)</h3>
              <ul className="space-y-2 text-eclipse-950">
                <li>• Modern sans-serif typeface</li>
                <li>• Optimized for user interfaces</li>
                <li>• Excellent legibility at all sizes</li>
                <li>• Clean and contemporary feel</li>
                <li>• Applied to: p, span, div, buttons, inputs</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="text-center">
          <button 
            onClick={() => window.history.back()}
            className="btn-outline"
          >
            ← Back to Application
          </button>
        </div>
        
      </div>
    </div>
  )
}
