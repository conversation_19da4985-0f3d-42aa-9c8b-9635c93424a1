import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { OnboardingProvider, useOnboarding } from '../contexts/OnboardingContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'

// Onboarding step components
import WelcomeStep from '../components/onboarding/WelcomeStep'
import NameStep from '../components/onboarding/NameStep'
import AgeStep from '../components/onboarding/AgeStep'
import PersonaStep from '../components/onboarding/PersonaStep'
import ConsentStep from '../components/onboarding/ConsentStep'

function OnboardingFlow() {
  const { user } = useAuth()
  const { currentStep, isLoading } = useOnboarding()
  const navigate = useNavigate()

  // Check if user has already completed onboarding
  useEffect(() => {
    if (user?.profileData?.onboardingCompleted) {
      navigate('/chat', { replace: true })
    }
  }, [user, navigate])

  // Show loading spinner during submission
  if (isLoading) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Enhanced background with warmer center - matching Image 2 */}
        <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700"></div>

        {/* Central warm glow effect with the specific warm center color #671700 */}
        <div className="absolute inset-0 bg-gradient-radial from-warm-center/30 via-dark-700/20 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-radial from-warm-center/20 via-transparent to-transparent"></div>

        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-white font-medium">Setting up your profile...</p>
          </div>
        </div>
      </div>
    )
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <NameStep />
      case 2:
        return <AgeStep />
      case 3:
        return <PersonaStep />
      case 4:
        return <ConsentStep />
      default:
        return <NameStep />
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced background with warmer center - matching Image 2 */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700"></div>

      {/* Central warm glow effect with the specific warm center color #671700 */}
      <div className="absolute inset-0 bg-gradient-radial from-warm-center/30 via-dark-700/20 to-transparent"></div>
      <div className="absolute inset-0 bg-gradient-radial from-warm-center/20 via-transparent to-transparent"></div>

      {/* Additional subtle warm tones */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-warm-center/10 to-transparent"></div>

      {/* Step content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-6">
        <div className="w-full max-w-lg">
          {renderStep()}
        </div>
      </div>
    </div>
  )
}

export default function OnboardingPage() {
  return (
    <OnboardingProvider>
      <OnboardingFlow />
    </OnboardingProvider>
  )
}
