import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useAdminAuth } from '../hooks/useAdminAuth'
import { apiService } from '../services/api'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'
import { EmotionCorrelationChart } from '../components/admin/EmotionCorrelationChart'
import { UserCohortChart } from '../components/admin/UserCohortChart'
import { EmotionFlowChart } from '../components/admin/EmotionFlowChart'
import { AdminUserManagement } from '../components/admin/AdminUserManagement'
import {
  Users,
  MessageSquare,
  Heart,
  TrendingUp,
  Activity,
  AlertCircle,
  Download,
  RefreshCw,
  BarChart3,
  Pie<PERSON><PERSON>,
  Settings
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    totalUsers: number
    activeSessions: number
    totalMessages: number
    // emotionCaptureRate: number
  }
  emotions: {
    topEmotions: Array<{ emotion: string; avgScore: number; count: number }>
    emotionTrends: Array<{ date: string; joy: number; excitement: number; calmness: number; anxiety: number }>
  }
  engagement: {
    avgSessionDuration: number
    avgMessagesPerSession: number
    returnUserRate: number
  }
  system: {
    errorRate: number
    activeConnections: number
  }
}

interface AdvancedAnalyticsData {
  emotionCorrelations: any[]
  userCohorts: any[]
  emotionFlow: any
  emotionAnomalies: any[]
  predictiveInsights: any[]
  userSegmentation: any
}

export default function AdminAnalyticsPage() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { isAdmin, isLoading: adminLoading, error: adminError } = useAdminAuth()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [advancedAnalytics, setAdvancedAnalytics] = useState<AdvancedAnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [advancedLoading, setAdvancedLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState('7d')
  const [activeTab, setActiveTab] = useState('overview')
  const [autoRefresh, setAutoRefresh] = useState(false)

  // Redirect non-admin users to /chat
  useEffect(() => {
    if (!adminLoading && !isAdmin) {
      navigate('/chat', { replace: true })
    }
  }, [isAdmin, adminLoading, navigate])

  useEffect(() => {
    if (!adminLoading && isAdmin) {
      fetchAnalytics()
      if (activeTab !== 'overview') {
        fetchAdvancedAnalytics()
      }
    }
  }, [timeRange, isAdmin, adminLoading, activeTab])

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh || !isAdmin) return

    const interval = setInterval(() => {
      fetchAnalytics()
      if (activeTab !== 'overview') {
        fetchAdvancedAnalytics()
      }
    }, 30000) // Refresh every 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh, isAdmin, activeTab])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch analytics data from admin endpoint
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90
      const adminAnalytics = await apiService.getAdminAnalytics(days)

      // Use real data from admin endpoint with fallbacks
      const analyticsData: AnalyticsData = adminAnalytics.data || {
        overview: {
          totalUsers: 0,
          activeSessions: 0,
          totalMessages: 0,
          // emotionCaptureRate: 0
        },
        emotions: {
          topEmotions: [],
          emotionTrends: []
        },
        engagement: {
          avgSessionDuration: 0,
          avgMessagesPerSession: 0,
          returnUserRate: 0
        },
        system: {
          errorRate: 0,
          activeConnections: 0
        }
      }

      setAnalytics(analyticsData)
    } catch (err) {
      console.error('Failed to fetch analytics:', err)
      setError('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const fetchAdvancedAnalytics = async () => {
    try {
      setAdvancedLoading(true)

      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90

      // Fetch all advanced analytics with individual error handling
      const results = await Promise.allSettled([
        apiService.getEmotionCorrelations(days),
        apiService.getUserCohorts(),
        apiService.getEmotionFlowAnalysis(24),
        apiService.getEmotionAnomalies(7),
        apiService.getUserSegmentation()
      ])

      // Extract successful results, use empty data for failed requests
      const [
        emotionCorrelationsRes,
        userCohortsRes,
        emotionFlowRes,
        emotionAnomaliesRes,
        userSegmentationRes
      ] = results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value
        } else {
          console.error(`Advanced analytics request ${index} failed:`, result.reason)
          return { data: null }
        }
      })

      setAdvancedAnalytics({
        emotionCorrelations: emotionCorrelationsRes.data?.correlations || [],
        userCohorts: userCohortsRes.data?.cohorts || [],
        emotionFlow: emotionFlowRes.data || { currentTrends: [], peakEmotions: [], emotionTransitions: [] },
        emotionAnomalies: emotionAnomaliesRes.data?.anomalies || [],
        predictiveInsights: [],
        userSegmentation: userSegmentationRes.data || { segments: [] }
      })
    } catch (err) {
      console.error('Failed to fetch advanced analytics:', err)
      // Don't set error for advanced analytics, just log it
    } finally {
      setAdvancedLoading(false)
    }
  }

  const exportData = async () => {
    try {
      const data = {
        basicAnalytics: analytics,
        advancedAnalytics: advancedAnalytics,
        exportedAt: new Date().toISOString(),
        timeRange
      }

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `ora-analytics-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Failed to export data:', err)
    }
  }

  // Show loading while checking admin status
  if (adminLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-gray-600">Checking admin access...</p>
        </div>
      </div>
    )
  }

  // This should not be reached due to redirect, but just in case
  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">Redirecting to chat...</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={fetchAnalytics}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <img
                src="/ora_logo.svg"
                alt="ORA Logo"
                className="w-16 h-16 object-contain"
              />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">ORA Analytics Dashboard</h1>
                <p className="text-gray-600 mt-1">Advanced insights into emotions, engagement, and system performance</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="autoRefresh"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="autoRefresh" className="text-sm text-gray-600">Auto-refresh</label>
              </div>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
              <button
                onClick={exportData}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
              <button
                onClick={() => {
                  fetchAnalytics()
                  if (activeTab !== 'overview') fetchAdvancedAnalytics()
                }}
                disabled={loading || advancedLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${(loading || advancedLoading) ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="border-t border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', name: 'Overview', icon: BarChart3 },
                { id: 'flow', name: 'Emotional Flow', icon: Heart },
                { id: 'cohorts', name: 'User Cohorts', icon: Users },
                { id: 'segmentation', name: 'User Segmentation', icon: PieChart },
                { id: 'admin', name: 'Admin Management', icon: Settings }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading && activeTab === 'overview' ? (
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner />
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        ) : (
          <>
            {/* Overview Tab */}
            {activeTab === 'overview' && analytics && (
              <>
                {/* Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{analytics?.overview.totalUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Activity className="w-8 h-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Sessions</p>
                <p className="text-2xl font-bold text-gray-900">{analytics?.overview.activeSessions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <MessageSquare className="w-8 h-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Messages</p>
                <p className="text-2xl font-bold text-gray-900">{analytics?.overview.totalMessages.toLocaleString()}</p>
              </div>
            </div>
          </div>

          {/* <div className="bg-white rounded-lg shadow p-6"> */}
            {/* <div className="flex items-center"> */}
              {/* <Heart className="w-8 h-8 text-red-600" /> */}
              {/* <div className="ml-4"> */}
                {/* <p className="text-sm font-medium text-gray-600">Emotion Capture</p> */}
                {/* <p className="text-2xl font-bold text-gray-900">{analytics?.overview.emotionCaptureRate}%</p> */}
              {/* </div> */}
            {/* </div> */}
          {/* </div> */}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Top Emotions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Heart className="w-5 h-5 text-red-500 mr-2" />
              Top Emotions
            </h3>
            <div className="space-y-3">
              {analytics?.emotions.topEmotions.map((emotion) => (
                <div key={emotion.emotion} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 capitalize">{emotion.emotion}</span>
                    <span className="ml-2 text-xs text-gray-500">({emotion.count} occurrences)</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" 
                        style={{ width: `${emotion.avgScore * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{(emotion.avgScore * 100).toFixed(1)}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Engagement Metrics */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <TrendingUp className="w-5 h-5 text-green-500 mr-2" />
              Engagement Metrics
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Avg Session Duration</span>
                <span className="text-lg font-semibold text-gray-900">{analytics?.engagement.avgSessionDuration} min</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Avg Messages/Session</span>
                <span className="text-lg font-semibold text-gray-900">{analytics?.engagement.avgMessagesPerSession}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Return User Rate</span>
                <span className="text-lg font-semibold text-gray-900">{analytics?.engagement.returnUserRate}%</span>
              </div>
            </div>
          </div>

          {/* System Health */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Activity className="w-5 h-5 text-blue-500 mr-2" />
              System Health
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Error Rate</span>
                <span className="text-lg font-semibold text-red-600">{analytics?.system.errorRate}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Active Connections</span>
                <span className="text-lg font-semibold text-gray-900">{analytics?.system.activeConnections}</span>
              </div>
            </div>
          </div>


        </div>
              </>
            )}



            {/* User Cohorts Tab */}
            {activeTab === 'cohorts' && (
              <div className="space-y-8">
                {advancedLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <LoadingSpinner />
                  </div>
                ) : (
                  advancedAnalytics?.userCohorts && (
                    <UserCohortChart data={advancedAnalytics.userCohorts} />
                  )
                )}
              </div>
            )}

            {/* Emotional Flow Tab - Combined with Intelligence */}
            {activeTab === 'flow' && (
              <div className="space-y-8">
                {advancedLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <>
                    {/* Header Section */}
                    <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 rounded-lg shadow p-6 text-white">
                      <h2 className="text-2xl font-bold mb-2">Emotional Flow & Intelligence</h2>
                      <p className="text-purple-100">
                        Comprehensive emotional analytics - track how emotions transition, correlate, and evolve during conversations. This is the major highlight of our emotional analytics platform.
                      </p>
                      <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div className="bg-white/10 rounded-lg p-3">
                          <div className="font-semibold">Current Trends</div>
                          <div className="text-purple-100">Rising/falling emotions</div>
                        </div>
                        <div className="bg-white/10 rounded-lg p-3">
                          <div className="font-semibold">Peak Detection</div>
                          <div className="text-purple-100">Highest intensity moments</div>
                        </div>
                        <div className="bg-white/10 rounded-lg p-3">
                          <div className="font-semibold">Transitions</div>
                          <div className="text-purple-100">Emotion-to-emotion flows</div>
                        </div>
                        <div className="bg-white/10 rounded-lg p-3">
                          <div className="font-semibold">Correlations</div>
                          <div className="text-purple-100">Emotion relationships</div>
                        </div>
                      </div>
                    </div>

                    {/* Emotion Flow Analysis */}
                    {advancedAnalytics?.emotionFlow && (
                      <EmotionFlowChart data={advancedAnalytics.emotionFlow} />
                    )}

                    {/* Emotion Correlations */}
                    {advancedAnalytics?.emotionCorrelations && (
                      <EmotionCorrelationChart data={advancedAnalytics.emotionCorrelations} />
                    )}

                    {/* Emotion Anomalies */}
                    {advancedAnalytics?.emotionAnomalies && advancedAnalytics.emotionAnomalies.length > 0 && (
                      <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                          Emotion Anomalies Detection
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                          Unusual emotional patterns that may require attention or indicate significant user experiences.
                        </p>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Emotion</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Score</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Expected</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Severity</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {advancedAnalytics.emotionAnomalies.slice(0, 10).map((anomaly: any, index: number) => (
                                <tr key={index} className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {anomaly.userId.substring(0, 8)}...
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                                    {anomaly.emotion}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {anomaly.score.toFixed(3)}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {anomaly.expectedScore.toFixed(3)}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                      anomaly.severity === 'high' ? 'bg-red-100 text-red-800' :
                                      anomaly.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-green-100 text-green-800'
                                    }`}>
                                      {anomaly.severity}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {new Date(anomaly.timestamp).toLocaleString()}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}



            {/* User Segmentation Tab */}
            {activeTab === 'segmentation' && (
              <div className="space-y-8">
                {advancedLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <>
                    {/* User Segments */}
                    {advancedAnalytics?.userSegmentation?.segments && (
                      <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">User Segments</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {advancedAnalytics.userSegmentation.segments.map((segment: any, index: number) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-6">
                              <h4 className="text-lg font-semibold text-gray-900 mb-2">{segment.name}</h4>
                              <div className="space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">Users:</span>
                                  <span className="text-sm font-medium">{segment.userCount.toLocaleString()}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">Retention:</span>
                                  <span className="text-sm font-medium">{(segment.retentionRate * 100).toFixed(1)}%</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">LTV:</span>
                                  <span className="text-sm font-medium">${segment.avgLifetimeValue}</span>
                                </div>
                                <div className="mt-4">
                                  <div className="text-xs text-gray-500">
                                    Avg Sessions: {segment.characteristics.avgSessions?.toFixed(1) || 'N/A'}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    Avg Duration: {segment.characteristics.avgDuration?.toFixed(1) || 'N/A'} min
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}

            {/* Admin Management Tab */}
            {activeTab === 'admin' && (
              <div className="space-y-8">
                <AdminUserManagement />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
