import { useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'

export default function AuthCallbackPage() {
  const navigate = useNavigate()
  const { setUserFromTokens } = useAuth()
  const [searchParams] = useSearchParams()

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const token = searchParams.get('token')
        const userStr = searchParams.get('user')

        if (token && userStr) {
          const refreshToken = searchParams.get('refresh_token')
          const userData = JSON.parse(userStr)

          // Set tokens and user data
          await setUserFromTokens({
            accessToken: token,
            refreshToken: refreshToken || '',
            expiresIn: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
          }, userData)

          // Navigate based on user status
          const needsOnboarding = !userData.profileData?.onboardingCompleted
          if (needsOnboarding) {
            navigate('/onboarding', { replace: true })
          } else {
            navigate('/chat', { replace: true })
          }
        } else {
          throw new Error('Missing authentication data')
        }
      } catch (error) {
        console.error('Auth callback error:', error)
        navigate('/auth/error?error=callback_failed')
      }
    }

    handleCallback()
  }, [searchParams, navigate, setUserFromTokens])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-600">Completing authentication...</p>
      </div>
    </div>
  )
}
