# GCP Migration Guide: Database and Admin Whitelist

This guide explains the current setup and how to migrate from localhost to GCP for both the database and admin whitelist functionality.

## Current Status ✅

### 1. Admin Whitelist System
- **✅ IMPLEMENTED**: Admin whitelist is already using database storage (not localStorage)
- **✅ CONFIGURED**: `<EMAIL>` has been added as super admin
- **✅ WORKING**: All admin authentication and authorization is database-driven
- **✅ CLOUD-READY**: The system will automatically work with GCP database once connected

### 2. Analytics Data Source
- **✅ IMPLEMENTED**: All analytics queries use the same database connection
- **✅ CLOUD-READY**: Analytics will automatically use GCP database once connected
- **✅ NO LOCALHOST DEPENDENCIES**: No hardcoded localhost references in analytics code

### 3. Database Configuration
- **🔄 CURRENT**: Using localhost PostgreSQL for development
- **🎯 TARGET**: GCP Cloud SQL PostgreSQL
- **✅ PREPARED**: Environment files configured for easy switching

## Quick Migration to GCP

### Option 1: Automated Setup (Recommended)
```bash
# Run the automated setup script
./scripts/setup-gcp-admin.sh

# This script will:
# 1. Check GCP connectivity
# 2. Update environment files
# 3. Test database connection
# 4. Add admin to whitelist
# 5. Verify everything is working
```

### Option 2: Manual Setup

#### Step 1: Update Database Connection
Edit `backend/.env`:
```env
# Comment out localhost
# DATABASE_URL=postgresql://postgres:@localhost:5432/ora_hume_db

# Uncomment GCP database
DATABASE_URL=postgresql://ora_user:@**************:5432/ora_hume_db
```

#### Step 2: Ensure Admin is in Whitelist
```bash
# Connect to GCP database
psql postgresql://ora_user:@**************:5432/ora_hume_db

# Add admin (run this SQL)
INSERT INTO admin_whitelist (email, role, permissions, notes) 
VALUES (
    '<EMAIL>', 
    'super_admin', 
    '{"canManageAdmins": true, "canViewAllAnalytics": true, "canExportData": true, "canManageSystem": true}',
    'Default admin - GCP setup'
) 
ON CONFLICT (email) DO UPDATE SET 
    role = 'super_admin',
    is_active = true;
```

#### Step 3: Restart Backend
```bash
cd backend
npm run dev
```

## Troubleshooting GCP Connection

### Issue: Password Required
**Symptoms**: `SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string`

**Solution**: The GCP database requires a password for the `ora_user`. You need to:

1. **Set a password for ora_user**:
   ```bash
   # Set password for ora_user
   gcloud sql users set-password ora_user \
     --instance=ora-postgres \
     --project=ora-phase1 \
     --password=YOUR_SECURE_PASSWORD
   ```

2. **Update connection string with password**:
   ```env
   # In backend/.env, update to include password:
   DATABASE_URL=************************************************************/ora_hume_db
   ```

### Issue: Missing Admin Tables
**Symptoms**: `relation "admin_whitelist" does not exist`

**Solution**: Run the admin tables setup script:
```bash
# First, connect and create the missing tables
psql *****************************************************/ora_hume_db -f scripts/setup-gcp-admin-tables.sql
```

### Issue: IPv6 Connection Problems
**Symptoms**: `Invalid flag for instance role: CloudSQL Second Generation doesn't support IPv6`

**Solutions**:
1. **Use IPv4 connection**:
   ```bash
   # Force IPv4 connection
   psql ************************************************/ora_hume_db
   ```

2. **Use Cloud SQL Proxy** (recommended for production):
   ```bash
   # Install Cloud SQL Proxy
   curl -o cloud_sql_proxy https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64
   chmod +x cloud_sql_proxy

   # Start proxy
   ./cloud_sql_proxy -instances=ora-phase1:us-central1:ora-postgres=tcp:5432

   # Connect via proxy
   DATABASE_URL=postgresql://ora_user:PASSWORD@localhost:5432/ora_hume_db
   ```

### Issue: Connection Timeout
**Symptoms**: `Connection terminated due to connection timeout`

**Solutions**:
1. **Check Firewall Rules**:
   ```bash
   # Get your current IP
   curl ifconfig.me

   # Add your IP to authorized networks
   gcloud sql instances patch ora-postgres \
     --project=ora-phase1 \
     --authorized-networks=$(curl -s ifconfig.me)/32
   ```

2. **Verify Database Instance**:
   ```bash
   # Check if instance is running
   gcloud sql instances list --project=ora-phase1

   # Get instance details
   gcloud sql instances describe ora-postgres --project=ora-phase1
   ```

## Verification Steps

### 1. Test Database Connection
```bash
# Should return "1" if successful
psql postgresql://ora_user:@**************:5432/ora_hume_db -c "SELECT 1;"
```

### 2. Verify Admin Whitelist
```bash
# Check admin is in whitelist
psql postgresql://ora_user:@**************:5432/ora_hume_db -c "
SELECT email, role, is_active FROM admin_whitelist WHERE email = '<EMAIL>';
"
```

### 3. Test Backend Connection
```bash
# Start backend and check logs
cd backend && npm run dev

# Look for:
# ✅ Database connected successfully
# (No connection timeout errors)
```

### 4. Test Analytics Dashboard
1. Start frontend: `cd frontend && npm run dev`
2. Login with `<EMAIL>`
3. Navigate to `/admin/analytics`
4. Verify data loads without errors

## Environment Files Summary

### Development (`backend/.env`)
```env
# Currently using localhost (working)
DATABASE_URL=postgresql://postgres:@localhost:5432/ora_hume_db

# GCP database (ready to switch)
# DATABASE_URL=postgresql://ora_user:@**************:5432/ora_hume_db

# Admin configuration
DEFAULT_ADMIN_EMAIL=<EMAIL>
```

### Production (`backend/.env.production`)
```env
# GCP database (configured)
DATABASE_URL=postgresql://ora_user:@**************:5432/ora_hume_db

# Admin configuration
DEFAULT_ADMIN_EMAIL=<EMAIL>
```

## Key Benefits of Current Setup

1. **Zero Code Changes Required**: All systems already use database storage
2. **Environment-Based Configuration**: Easy switching between localhost and GCP
3. **Admin Already Configured**: `<EMAIL>` is ready to use
4. **Analytics Ready**: All analytics queries will automatically use GCP database
5. **Production Ready**: Environment files configured for deployment

## Next Steps

1. **Immediate**: Use localhost setup (currently working)
2. **When Ready**: Run `./scripts/setup-gcp-admin.sh` to migrate to GCP
3. **Production**: Deploy with GCP database configuration

The system is fully prepared for GCP migration - it's just a matter of establishing the database connection and switching the environment variable.
