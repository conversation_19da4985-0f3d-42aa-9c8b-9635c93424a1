# OAuth Client ID Fix Guide

## Problem
The OAuth client is showing "invalid_client" error with code 401, which typically means:
1. The client_id has extra whitespace/newlines
2. The Google OAuth console doesn't have the correct redirect URIs configured
3. The client_id doesn't match what's configured in Google Console

## Solution Steps

### 1. ✅ Fixed Client ID Whitespace (COMPLETED)
- Added `.trim()` to client ID and secret in backend code
- Updated Google Secrets with clean values (no newlines)
- Redeploying with fixed code

### 2. 🔧 Google OAuth Console Configuration (REQUIRED)

You need to update your Google OAuth console with the correct redirect URIs:

#### Go to Google Cloud Console:
1. Visit: https://console.cloud.google.com/
2. Select project: `ora-phase1`
3. Navigate to: **APIs & Services > Credentials**
4. Find your OAuth 2.0 Client ID: `************-rk4mmk3gf8egug0e0ubm479to12r6ba1.apps.googleusercontent.com`

#### Update Authorized Redirect URIs:
Make sure these URIs are configured:

**Development:**
- `http://localhost:3001/api/auth/google/callback`

**Production:**
- `https://ora-backend-************.us-central1.run.app/api/auth/google/callback`

#### Update Authorized JavaScript Origins:
Make sure these origins are configured:

**Development:**
- `http://localhost:3000`
- `http://localhost:3001`

**Production:**
- `https://ora-frontend-************.us-central1.run.app`
- `https://ora-backend-************.us-central1.run.app`

### 3. 🧪 Testing Steps

After updating Google Console:

1. **Test Backend OAuth URL:**
   ```bash
   curl -I https://ora-backend-************.us-central1.run.app/api/auth/google
   ```

2. **Test Frontend Login:**
   - Go to: https://ora-frontend-************.us-central1.run.app/login
   - Click login button
   - Should redirect to Google without "invalid_client" error

### 4. 🔍 Troubleshooting

If you still see issues:

1. **Check Google Console Configuration:**
   - Verify all redirect URIs are exactly correct
   - No extra spaces or characters
   - HTTPS for production, HTTP for localhost

2. **Clear Browser Cache:**
   - Clear cookies and cache for your domain
   - Try in incognito/private mode

3. **Check Client ID Match:**
   - Ensure the client ID in Google Console matches exactly:
     `************-rk4mmk3gf8egug0e0ubm479to12r6ba1.apps.googleusercontent.com`

### 5. 📋 Current Configuration Summary

**Frontend URL:** https://ora-frontend-************.us-central1.run.app
**Backend URL:** https://ora-backend-************.us-central1.run.app
**OAuth Callback:** https://ora-backend-************.us-central1.run.app/api/auth/google/callback

**Client ID:** ************-rk4mmk3gf8egug0e0ubm479to12r6ba1.apps.googleusercontent.com

## Next Steps

1. ⏳ Wait for current deployment to complete
2. 🔧 Update Google OAuth console with correct redirect URIs
3. 🧪 Test the login flow
4. ✅ Verify no more "invalid_client" errors
