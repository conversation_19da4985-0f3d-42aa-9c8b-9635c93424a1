-- Add admin to local database whitelist
-- Run this with: psql postgresql://postgres:@localhost:5432/ora_hume_db -f scripts/add-local-admin.sql

-- Ensure the admin_whitelist table exists
CREATE TABLE IF NOT EXISTS admin_whitelist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    role VARCHAR(50) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin', 'viewer')),
    added_by UUI<PERSON>,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_access TIMESTAMP WITH TIME ZONE,
    permissions JSONB DEFAULT '{}',
    notes TEXT
);

-- Add <EMAIL> as super admin
INSERT INTO admin_whitelist (email, role, permissions, notes) 
VALUES (
    '<EMAIL>', 
    'super_admin', 
    '{"canManageAdmins": true, "canViewAllAnalytics": true, "canExportData": true, "canManageSystem": true}',
    'Default admin - added via setup script'
) 
ON CONFLICT (email) DO UPDATE SET 
    role = 'super_admin',
    is_active = true,
    permissions = '{"canManageAdmins": true, "canViewAllAnalytics": true, "canExportData": true, "canManageSystem": true}',
    notes = 'Default admin - updated via setup script',
    added_at = CURRENT_TIMESTAMP;

-- Verify the admin was added
SELECT 
    email, 
    role, 
    is_active, 
    permissions, 
    added_at,
    notes
FROM admin_whitelist 
WHERE email = '<EMAIL>';

-- Show all admins
SELECT 
    email, 
    role, 
    is_active, 
    added_at
FROM admin_whitelist 
ORDER BY added_at DESC;
