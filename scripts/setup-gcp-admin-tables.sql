-- Setup admin tables for GCP database
-- Run this with: psql postgresql://ora_user:@************:5432/ora_hume_db -f scripts/setup-gcp-admin-tables.sql

-- Admin whitelist table for access control
CREATE TABLE IF NOT EXISTS admin_whitelist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    role VARCHAR(50) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin', 'viewer')),
    added_by UUID REFERENCES users(id),
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_access TIMESTAMP WITH TIME ZONE,
    permissions JSONB DEFAULT '{}',
    notes TEXT
);

-- Indexes for admin whitelist
CREATE INDEX IF NOT EXISTS idx_admin_whitelist_email ON admin_whitelist(email);
CREATE INDEX IF NOT EXISTS idx_admin_whitelist_active ON admin_whitelist(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_whitelist_role ON admin_whitelist(role);

-- Admin activity log for security and auditing
CREATE TABLE IF NOT EXISTS admin_activity_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_email VARCHAR(255) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(255),
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255)
);

-- Indexes for admin activity log
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_email ON admin_activity_log(admin_email);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_action ON admin_activity_log(action);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created_at ON admin_activity_log(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_resource ON admin_activity_log(resource);

-- Audio data table for voice storage analytics
CREATE TABLE IF NOT EXISTS audio_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL DEFAULT 0,
    duration_ms INTEGER,
    format VARCHAR(20) DEFAULT 'webm',
    upload_status VARCHAR(50) DEFAULT 'pending' CHECK (upload_status IN ('pending', 'uploading', 'completed', 'failed')),
    cloud_storage_path VARCHAR(500),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for audio data
CREATE INDEX IF NOT EXISTS idx_audio_data_user_id ON audio_data(user_id);
CREATE INDEX IF NOT EXISTS idx_audio_data_session_id ON audio_data(session_id);
CREATE INDEX IF NOT EXISTS idx_audio_data_upload_status ON audio_data(upload_status);
CREATE INDEX IF NOT EXISTS idx_audio_data_created_at ON audio_data(created_at);

-- Voice storage queue for background processing
CREATE TABLE IF NOT EXISTS voice_storage_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    audio_data BYTEA NOT NULL,
    file_size INTEGER NOT NULL,
    sequence_number INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    cloud_path VARCHAR(500),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for voice storage queue
CREATE INDEX IF NOT EXISTS idx_voice_storage_queue_status ON voice_storage_queue(status);
CREATE INDEX IF NOT EXISTS idx_voice_storage_queue_session_user ON voice_storage_queue(session_id, user_id, role);
CREATE INDEX IF NOT EXISTS idx_voice_storage_queue_created_at ON voice_storage_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_voice_storage_queue_updated_at ON voice_storage_queue(updated_at);

-- Add <EMAIL> as super admin
INSERT INTO admin_whitelist (email, role, permissions, notes) 
VALUES (
    '<EMAIL>', 
    'super_admin', 
    '{"canManageAdmins": true, "canViewAllAnalytics": true, "canExportData": true, "canManageSystem": true}',
    'Default admin - added via GCP setup script'
) 
ON CONFLICT (email) DO UPDATE SET 
    role = 'super_admin',
    is_active = true,
    permissions = '{"canManageAdmins": true, "canViewAllAnalytics": true, "canExportData": true, "canManageSystem": true}',
    notes = 'Default admin - updated via GCP setup script',
    added_at = CURRENT_TIMESTAMP;

-- Verify the admin was added
SELECT 
    email, 
    role, 
    is_active, 
    permissions, 
    added_at,
    notes
FROM admin_whitelist 
WHERE email = '<EMAIL>';

-- Show all admins
SELECT 
    email, 
    role, 
    is_active, 
    added_at
FROM admin_whitelist 
ORDER BY added_at DESC;

-- Show table creation summary
SELECT 
    'admin_whitelist' as table_name,
    COUNT(*) as record_count
FROM admin_whitelist
UNION ALL
SELECT 
    'admin_activity_log' as table_name,
    COUNT(*) as record_count
FROM admin_activity_log
UNION ALL
SELECT 
    'audio_data' as table_name,
    COUNT(*) as record_count
FROM audio_data
UNION ALL
SELECT 
    'voice_storage_queue' as table_name,
    COUNT(*) as record_count
FROM voice_storage_queue;
