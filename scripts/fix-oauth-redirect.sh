#!/bin/bash

# Fix OAuth redirect issue by redeploying with correct BACKEND_URL
# This script addresses the issue where production OAuth redirects to localhost

set -e

PROJECT_ID="ora-phase1"
REGION="us-central1"
BACKEND_URL="https://ora-backend-************.us-central1.run.app"
FRONTEND_URL="https://ora-frontend-************.us-central1.run.app"

echo "🔧 Fixing OAuth redirect issue for ORA deployment"
echo "📋 Project ID: $PROJECT_ID"
echo "🌍 Region: $REGION"
echo "🔗 Backend URL: $BACKEND_URL"
echo "🔗 Frontend URL: $FRONTEND_URL"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
    echo "❌ You are not authenticated with gcloud. Please run 'gcloud auth login' first."
    exit 1
fi

# Set the project
echo "🔧 Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

echo ""
echo "🚀 Redeploying backend with correct BACKEND_URL..."

# Redeploy backend with the correct BACKEND_URL environment variable
gcloud run deploy ora-backend \
  --region=$REGION \
  --platform=managed \
  --allow-unauthenticated \
  --set-env-vars="NODE_ENV=production,BACKEND_URL=$BACKEND_URL,FRONTEND_URL=$FRONTEND_URL,CORS_ORIGINS=$FRONTEND_URL,DEFAULT_ADMIN_EMAIL=<EMAIL>" \
  --memory=1Gi \
  --cpu=1 \
  --max-instances=10 \
  --concurrency=10 \
  --timeout=300

echo ""
echo "✅ Backend redeployed successfully!"
echo ""
echo "🧪 Testing OAuth redirect URL..."

# Test the OAuth endpoint to verify the redirect URI is correct
OAUTH_RESPONSE=$(curl -s -I "https://ora-backend-************.us-central1.run.app/api/auth/google" | grep -i location || true)

if [[ $OAUTH_RESPONSE == *"localhost"* ]]; then
    echo "❌ OAuth redirect still pointing to localhost!"
    echo "   Response: $OAUTH_RESPONSE"
    echo ""
    echo "🔍 Troubleshooting steps:"
    echo "1. Check if BACKEND_URL environment variable is set correctly in Cloud Run"
    echo "2. Verify the backend service is using the updated environment variables"
    echo "3. Check Cloud Run logs for any errors"
    exit 1
else
    echo "✅ OAuth redirect URL is now correct!"
    echo "   Response: $OAUTH_RESPONSE"
fi

echo ""
echo "🎉 OAuth redirect issue has been fixed!"
echo ""
echo "📋 Next steps:"
echo "1. Test the login flow at: $FRONTEND_URL/login"
echo "2. Verify that OAuth redirects to the production backend instead of localhost"
echo "3. Ensure the Google OAuth console has the correct redirect URI configured:"
echo "   - $BACKEND_URL/api/auth/google/callback"
echo ""
echo "💡 If you still see localhost redirects, you may need to:"
echo "1. Update Google OAuth console redirect URIs"
echo "2. Clear browser cache and cookies"
echo "3. Check Cloud Run environment variables in the console"
