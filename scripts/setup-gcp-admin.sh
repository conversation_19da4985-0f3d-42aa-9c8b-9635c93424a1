#!/bin/bash

# Setup script for GCP database connection and admin whitelist
# This script helps you configure the GCP database and ensure admin whitelist is working

set -e

PROJECT_ID=${1:-"ora-phase1"}
REGION=${2:-"us-central1"}
DB_INSTANCE_NAME="ora-postgres"
DB_NAME="ora_hume_db"
DB_USER="ora_user"
ADMIN_EMAIL="<EMAIL>"

echo "🔧 Setting up GCP database connection and admin whitelist"
echo "📋 Project ID: $PROJECT_ID"
echo "🌍 Region: $REGION"
echo "🗄️ Database Instance: $DB_INSTANCE_NAME"
echo "👤 Admin Email: $ADMIN_EMAIL"
echo ""

# Function to check if gcloud is installed and authenticated
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        echo "❌ gcloud CLI is not installed. Please install it first."
        exit 1
    fi

    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
        echo "❌ You are not authenticated with gcloud. Please run 'gcloud auth login' first."
        exit 1
    fi
}

# Function to get database IP
get_database_ip() {
    echo "🔍 Getting database IP address..."
    DB_IP=$(gcloud sql instances describe $DB_INSTANCE_NAME --project=$PROJECT_ID --format="value(ipAddresses[0].ipAddress)" 2>/dev/null || echo "")
    
    if [ -z "$DB_IP" ]; then
        echo "❌ Could not get database IP. Make sure the instance exists and you have access."
        echo "   Run: gcloud sql instances list --project=$PROJECT_ID"
        exit 1
    fi
    
    echo "✅ Database IP: $DB_IP"
}

# Function to check database connectivity
test_database_connection() {
    echo "🔌 Testing database connection..."
    
    # Try to connect using psql
    if command -v psql &> /dev/null; then
        if psql "postgresql://$DB_USER:@$DB_IP:5432/$DB_NAME" -c "SELECT 1;" &>/dev/null; then
            echo "✅ Database connection successful"
            return 0
        else
            echo "❌ Database connection failed"
            return 1
        fi
    else
        echo "⚠️  psql not found. Skipping direct connection test."
        return 0
    fi
}

# Function to update environment files
update_env_files() {
    echo "📝 Updating environment files..."
    
    # Update backend/.env
    if [ -f "backend/.env" ]; then
        # Comment out localhost database
        sed -i.bak 's/^DATABASE_URL=postgresql:\/\/postgres:@localhost/# DATABASE_URL=postgresql:\/\/postgres:@localhost/' backend/.env
        
        # Uncomment or add GCP database URL
        if grep -q "DATABASE_URL=postgresql://ora_user:@$DB_IP" backend/.env; then
            sed -i.bak "s/^# DATABASE_URL=postgresql:\/\/ora_user:@$DB_IP/DATABASE_URL=postgresql:\/\/ora_user:@$DB_IP/" backend/.env
        else
            echo "DATABASE_URL=postgresql://ora_user:@$DB_IP:5432/ora_hume_db" >> backend/.env
        fi
        
        # Ensure DEFAULT_ADMIN_EMAIL is set
        if ! grep -q "DEFAULT_ADMIN_EMAIL" backend/.env; then
            echo "DEFAULT_ADMIN_EMAIL=$ADMIN_EMAIL" >> backend/.env
        fi
        
        echo "✅ Updated backend/.env"
    fi
    
    # Update backend/.env.production
    if [ -f "backend/.env.production" ]; then
        sed -i.bak "s|DATABASE_URL=.*|DATABASE_URL=postgresql://ora_user:@$DB_IP:5432/ora_hume_db|" backend/.env.production
        
        if ! grep -q "DEFAULT_ADMIN_EMAIL" backend/.env.production; then
            echo "DEFAULT_ADMIN_EMAIL=$ADMIN_EMAIL" >> backend/.env.production
        fi
        
        echo "✅ Updated backend/.env.production"
    fi
}

# Function to setup database schema and admin
setup_database() {
    echo "🗄️ Setting up database schema and admin..."
    
    if command -v psql &> /dev/null; then
        echo "📋 Running database setup..."
        
        # Check if we can connect and run setup
        if psql "postgresql://$DB_USER:@$DB_IP:5432/$DB_NAME" -f setup-gcp-database.sql; then
            echo "✅ Database schema setup completed"
        else
            echo "❌ Database schema setup failed"
            echo "   You may need to run this manually:"
            echo "   psql postgresql://$DB_USER:@$DB_IP:5432/$DB_NAME -f setup-gcp-database.sql"
        fi
        
        # Add admin to whitelist
        echo "👤 Adding admin to whitelist..."
        psql "postgresql://$DB_USER:@$DB_IP:5432/$DB_NAME" -c "
            INSERT INTO admin_whitelist (email, role, permissions, notes) 
            VALUES (
                '$ADMIN_EMAIL', 
                'super_admin', 
                '{\"canManageAdmins\": true, \"canViewAllAnalytics\": true, \"canExportData\": true, \"canManageSystem\": true}',
                'Default admin - added via setup script'
            ) 
            ON CONFLICT (email) DO UPDATE SET 
                role = 'super_admin',
                is_active = true,
                permissions = '{\"canManageAdmins\": true, \"canViewAllAnalytics\": true, \"canExportData\": true, \"canManageSystem\": true}',
                notes = 'Default admin - updated via setup script';
        " && echo "✅ Admin added to whitelist" || echo "❌ Failed to add admin to whitelist"
    else
        echo "⚠️  psql not found. Please install PostgreSQL client tools."
        echo "   On macOS: brew install postgresql"
        echo "   On Ubuntu: sudo apt-get install postgresql-client"
    fi
}

# Function to check firewall rules
check_firewall() {
    echo "🔥 Checking firewall rules..."
    
    # Get current IP
    CURRENT_IP=$(curl -s ifconfig.me || echo "unknown")
    echo "📍 Your current IP: $CURRENT_IP"
    
    # Check if there are any authorized networks
    AUTHORIZED_NETWORKS=$(gcloud sql instances describe $DB_INSTANCE_NAME --project=$PROJECT_ID --format="value(settings.ipConfiguration.authorizedNetworks[].value)" 2>/dev/null || echo "")
    
    if [ -z "$AUTHORIZED_NETWORKS" ]; then
        echo "⚠️  No authorized networks found. You may need to add your IP:"
        echo "   gcloud sql instances patch $DB_INSTANCE_NAME --project=$PROJECT_ID --authorized-networks=$CURRENT_IP/32"
    else
        echo "✅ Authorized networks: $AUTHORIZED_NETWORKS"
        if echo "$AUTHORIZED_NETWORKS" | grep -q "$CURRENT_IP"; then
            echo "✅ Your IP is authorized"
        else
            echo "⚠️  Your IP ($CURRENT_IP) is not in authorized networks"
            echo "   Add it with: gcloud sql instances patch $DB_INSTANCE_NAME --project=$PROJECT_ID --authorized-networks=$AUTHORIZED_NETWORKS,$CURRENT_IP/32"
        fi
    fi
}

# Main execution
main() {
    echo "🚀 Starting GCP database and admin setup..."
    echo ""
    
    check_gcloud
    get_database_ip
    check_firewall
    
    if test_database_connection; then
        update_env_files
        setup_database
        
        echo ""
        echo "🎉 Setup completed successfully!"
        echo ""
        echo "✅ Database connection configured"
        echo "✅ Admin whitelist configured with: $ADMIN_EMAIL"
        echo ""
        echo "🔄 Next steps:"
        echo "1. Restart your backend server: cd backend && npm run dev"
        echo "2. Test admin access by logging in with: $ADMIN_EMAIL"
        echo "3. Visit the analytics dashboard to verify everything is working"
        echo ""
    else
        echo ""
        echo "❌ Database connection failed. Please check:"
        echo "1. Database instance is running: gcloud sql instances list --project=$PROJECT_ID"
        echo "2. Your IP is authorized (see firewall check above)"
        echo "3. Database user has correct permissions"
        echo ""
        echo "🔧 Manual setup commands:"
        echo "   # Add your IP to authorized networks:"
        echo "   gcloud sql instances patch $DB_INSTANCE_NAME --project=$PROJECT_ID --authorized-networks=\$(curl -s ifconfig.me)/32"
        echo ""
        echo "   # Connect to database manually:"
        echo "   gcloud sql connect $DB_INSTANCE_NAME --user=$DB_USER --project=$PROJECT_ID"
    fi
}

# Run main function
main "$@"
