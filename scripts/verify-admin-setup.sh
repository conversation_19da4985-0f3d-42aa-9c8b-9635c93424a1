#!/bin/bash

# Verification script for admin whitelist setup
# This script checks if the admin whitelist is properly configured

set -e

ADMIN_EMAIL="<EMAIL>"
DATABASE_URL=${DATABASE_URL:-"postgresql://postgres:@localhost:5432/ora_hume_db"}

echo "🔍 Verifying Admin Whitelist Setup"
echo "📧 Admin Email: $ADMIN_EMAIL"
echo "🗄️ Database: $DATABASE_URL"
echo ""

# Function to check if psql is available
check_psql() {
    if ! command -v psql &> /dev/null; then
        echo "❌ psql not found. Please install PostgreSQL client tools."
        echo "   On macOS: brew install postgresql"
        echo "   On Ubuntu: sudo apt-get install postgresql-client"
        exit 1
    fi
}

# Function to test database connection
test_connection() {
    echo "🔌 Testing database connection..."
    if psql "$DATABASE_URL" -c "SELECT 1;" &>/dev/null; then
        echo "✅ Database connection successful"
        return 0
    else
        echo "❌ Database connection failed"
        return 1
    fi
}

# Function to check admin whitelist table
check_table() {
    echo "📋 Checking admin_whitelist table..."
    
    # Check if table exists
    TABLE_EXISTS=$(psql "$DATABASE_URL" -t -c "
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'admin_whitelist'
        );
    " | xargs)
    
    if [ "$TABLE_EXISTS" = "t" ]; then
        echo "✅ admin_whitelist table exists"
    else
        echo "❌ admin_whitelist table does not exist"
        echo "   Run: psql $DATABASE_URL -f backend/src/database/schema.sql"
        return 1
    fi
}

# Function to check admin user
check_admin() {
    echo "👤 Checking admin user: $ADMIN_EMAIL"
    
    ADMIN_DATA=$(psql "$DATABASE_URL" -t -c "
        SELECT 
            email, 
            role, 
            is_active, 
            permissions::text,
            added_at
        FROM admin_whitelist 
        WHERE email = '$ADMIN_EMAIL';
    ")
    
    if [ -n "$ADMIN_DATA" ]; then
        echo "✅ Admin user found:"
        echo "$ADMIN_DATA" | while IFS='|' read -r email role active perms added_at; do
            echo "   📧 Email: $(echo $email | xargs)"
            echo "   🔑 Role: $(echo $role | xargs)"
            echo "   ✅ Active: $(echo $active | xargs)"
            echo "   📅 Added: $(echo $added_at | xargs)"
            echo "   🛡️ Permissions: $(echo $perms | xargs)"
        done
    else
        echo "❌ Admin user not found"
        echo "   Run: psql $DATABASE_URL -f scripts/add-local-admin.sql"
        return 1
    fi
}

# Function to check all admins
list_all_admins() {
    echo ""
    echo "📊 All admin users:"
    
    psql "$DATABASE_URL" -c "
        SELECT 
            email, 
            role, 
            is_active, 
            added_at
        FROM admin_whitelist 
        ORDER BY added_at DESC;
    "
}

# Function to test backend server
test_backend() {
    echo ""
    echo "🚀 Testing backend server..."
    
    if curl -s http://localhost:3001/health &>/dev/null; then
        HEALTH_DATA=$(curl -s http://localhost:3001/health | jq -r '.data.database // "unknown"' 2>/dev/null || echo "unknown")
        echo "✅ Backend server is running"
        echo "   🗄️ Database status: $HEALTH_DATA"
    else
        echo "⚠️  Backend server is not running"
        echo "   Start with: cd backend && npm run dev"
    fi
}

# Main execution
main() {
    check_psql
    
    if test_connection; then
        check_table && check_admin
        list_all_admins
        test_backend
        
        echo ""
        echo "🎉 Admin whitelist verification completed!"
        echo ""
        echo "✅ Summary:"
        echo "   - Database connection: Working"
        echo "   - Admin whitelist table: Exists"
        echo "   - Admin user ($ADMIN_EMAIL): Configured"
        echo "   - Backend server: $(curl -s http://localhost:3001/health &>/dev/null && echo "Running" || echo "Not running")"
        echo ""
        echo "🔄 Next steps:"
        echo "1. Start backend: cd backend && npm run dev"
        echo "2. Start frontend: cd frontend && npm run dev"
        echo "3. Login with: $ADMIN_EMAIL"
        echo "4. Access admin panel: http://localhost:3000/admin/analytics"
        echo ""
    else
        echo ""
        echo "❌ Database connection failed. Please check:"
        echo "1. Database is running"
        echo "2. Connection string is correct: $DATABASE_URL"
        echo "3. User has proper permissions"
        echo ""
    fi
}

# Run main function
main "$@"
